<?php
// ติดตั้งฟอนต์ TH Sarabun New แบบง่าย
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 ติดตั้งฟอนต์ TH Sarabun New</h2>";

require_once 'vendor/autoload.php';

// สร้างโฟลเดอร์ fonts
$fontsDir = __DIR__ . '/fonts';
if (!is_dir($fontsDir)) {
    mkdir($fontsDir, 0755, true);
    echo "<p style='color: green;'>✅ สร้างโฟลเดอร์ fonts</p>";
}

// ดาวน์โหลดฟอนต์ TH Sarabun New จาก Google Fonts
$fontFiles = [
    'THSarabunNew.ttf' => 'https://fonts.gstatic.com/s/sarabun/v13/DtVhJx26TKEqsc-lWJNJ2QKBUwQ.ttf',
    'THSarabunNew-Bold.ttf' => 'https://fonts.gstatic.com/s/sarabun/v13/DtVjJx26TKEqsc-lWJNJ2QKBUwQhBbI.ttf'
];

foreach ($fontFiles as $filename => $url) {
    $fontPath = $fontsDir . '/' . $filename;
    
    if (!file_exists($fontPath)) {
        echo "<p>📥 กำลังดาวน์โหลด $filename...</p>";
        
        // ใช้ curl หรือ file_get_contents
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);
        
        $fontData = file_get_contents($url, false, $context);
        if ($fontData !== false) {
            file_put_contents($fontPath, $fontData);
            echo "<p style='color: green;'>✅ ดาวน์โหลด $filename สำเร็จ (" . number_format(strlen($fontData)) . " bytes)</p>";
        } else {
            echo "<p style='color: red;'>❌ ไม่สามารถดาวน์โหลด $filename ได้</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ $filename มีอยู่แล้ว</p>";
    }
}

// ทดสอบการแปลงฟอนต์
echo "<h3>🧪 ทดสอบการแปลงฟอนต์</h3>";

try {
    $fontPath = $fontsDir . '/THSarabunNew.ttf';
    
    if (file_exists($fontPath)) {
        // ลองแปลงฟอนต์
        $convertedFont = TCPDF_FONTS::addTTFfont($fontPath, 'TrueTypeUnicode', '', 96);
        
        if ($convertedFont) {
            echo "<p style='color: green;'>✅ แปลงฟอนต์สำเร็จ: $convertedFont</p>";
            
            // ทดสอบการใช้งาน
            $pdf = new TCPDF('P', PDF_UNIT, 'A4', true, 'UTF-8', false);
            $pdf->AddPage();
            $pdf->SetFont($convertedFont, '', 16);
            $pdf->Cell(0, 10, 'ทดสอบฟอนต์ TH Sarabun New', 0, 1, 'C');
            
            echo "<p style='color: green;'>✅ ฟอนต์ใช้งานได้แล้ว!</p>";
            
        } else {
            echo "<p style='color: red;'>❌ ไม่สามารถแปลงฟอนต์ได้</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ ไม่พบไฟล์ฟอนต์</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "</p>";
}

echo "<h3>📋 ขั้นตอนต่อไป</h3>";
echo "<ol>";
echo "<li><a href='test_thai_font.php' target='_blank'>ทดสอบฟอนต์ไทย</a></li>";
echo "<li><a href='export_pdf.php' target='_blank'>ทดสอบรายงาน PDF</a></li>";
echo "<li>ตรวจสอบการแสดงผลภาษาไทย</li>";
echo "</ol>";

// สร้างไฟล์ helper สำหรับใช้ฟอนต์
$helperCode = '<?php
// Helper function สำหรับใช้ฟอนต์ TH Sarabun New
function setupThaiFont($pdf) {
    $fontPath = __DIR__ . "/fonts/THSarabunNew.ttf";
    
    if (file_exists($fontPath)) {
        try {
            $fontName = TCPDF_FONTS::addTTFfont($fontPath, "TrueTypeUnicode", "", 96);
            return $fontName;
        } catch (Exception $e) {
            return "helvetica"; // fallback
        }
    }
    
    return "helvetica"; // fallback
}
?>';

file_put_contents('thai_font_helper.php', $helperCode);
echo "<p style='color: green;'>✅ สร้างไฟล์ thai_font_helper.php</p>";
?>
