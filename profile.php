<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตรวจสอบการล็อกอิน
requireLogin();

$message = '';
$messageType = '';

// ดึงข้อมูลผู้ใช้ปัจจุบัน
$currentUser = $auth->getCurrentUser();

// ประมวลผลการเปลี่ยนรหัสผ่าน
if ($_POST && isset($_POST['change_password'])) {
    $oldPassword = $_POST['old_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    if (empty($oldPassword) || empty($newPassword) || empty($confirmPassword)) {
        $message = 'กรุณากรอกข้อมูลให้ครบถ้วน';
        $messageType = 'danger';
    } elseif ($newPassword !== $confirmPassword) {
        $message = 'รหัสผ่านใหม่ไม่ตรงกัน';
        $messageType = 'danger';
    } elseif (strlen($newPassword) < 6) {
        $message = 'รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร';
        $messageType = 'danger';
    } else {
        if ($auth->changePassword($_SESSION['user_id'], $oldPassword, $newPassword)) {
            $message = 'เปลี่ยนรหัสผ่านสำเร็จ';
            $messageType = 'success';
        } else {
            $message = 'รหัสผ่านเดิมไม่ถูกต้อง';
            $messageType = 'danger';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>โปรไฟล์ - Asset Management System</title>

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ระบบจัดการ Asset</h1>
            <p class="subtitle">Asset Management System - จัดการทรัพย์สินองค์กรอย่างมีประสิทธิภาพ</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="index.php"><i class="fas fa-list"></i> รายการ Assets</a></li>
                <?php if (isAdmin()): ?>
                    <li><a href="users.php"><i class="fas fa-users"></i> จัดการผู้ใช้</a></li>
                <?php endif; ?>
                <li><a href="#" onclick="openToolsModal()"><i class="fas fa-tools"></i> เครื่องมือ</a></li>
                <li><a href="#" onclick="openProfileModal()" class="active"><i class="fas fa-user"></i> โปรไฟล์</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> ออกจากระบบ</a></li>
            </ul>
        </nav>

        <!-- User Info Bar -->
        <div class="user-info-bar">
            <div class="container">
                <span class="welcome-text">ยินดีต้อนรับ, <strong><?= getCurrentUserFullName() ?></strong></span>
                <span class="role-badge badge badge-<?= isAdmin() ? 'success' : 'secondary' ?>">
                    <?= getCurrentUserRole() ?>
                </span>
            </div>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <div class="form-row">
            <!-- ข้อมูลโปรไฟล์ -->
            <div class="card">
                <div class="card-header">
                    <h2>ข้อมูลโปรไฟล์</h2>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label>Username:</label>
                        <p><span class="asset-id"><?= htmlspecialchars($currentUser['username']) ?></span></p>
                    </div>
                    
                    <div class="form-group">
                        <label>ชื่อ-นามสกุล:</label>
                        <p><?= htmlspecialchars($currentUser['full_name']) ?></p>
                    </div>
                    
                    <div class="form-group">
                        <label>Email:</label>
                        <p><?= htmlspecialchars($currentUser['email']) ?></p>
                    </div>
                    
                    <?php if (isset($currentUser['role'])): ?>
                    <div class="form-group">
                        <label>Role:</label>
                        <p>
                            <span class="badge badge-<?= $currentUser['role'] === 'Admin' ? 'success' : 'secondary' ?>">
                                <?= htmlspecialchars($currentUser['role']) ?>
                            </span>
                        </p>
                    </div>
                    <?php endif; ?>

                    <?php if (isset($currentUser['status'])): ?>
                    <div class="form-group">
                        <label>Status:</label>
                        <p>
                            <span class="badge badge-<?= $currentUser['status'] === 'Active' ? 'success' : 'danger' ?>">
                                <?= htmlspecialchars($currentUser['status']) ?>
                            </span>
                        </p>
                    </div>
                    <?php endif; ?>

                    <?php if (isset($currentUser['last_login'])): ?>
                    <div class="form-group">
                        <label>Last Login:</label>
                        <p><?= $currentUser['last_login'] ? formatDateTime($currentUser['last_login']) : '-' ?></p>
                    </div>
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label>วันที่สร้างบัญชี:</label>
                        <p><?= formatDateTime($currentUser['created_date']) ?></p>
                    </div>
                </div>
            </div>

            <!-- เปลี่ยนรหัสผ่าน -->
            <div class="card">
                <div class="card-header">
                    <h2>เปลี่ยนรหัสผ่าน</h2>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <div class="form-group">
                            <label for="old_password">รหัสผ่านเดิม *</label>
                            <input type="password" id="old_password" name="old_password" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="new_password">รหัสผ่านใหม่ *</label>
                            <input type="password" id="new_password" name="new_password" class="form-control" 
                                   minlength="6" required>
                            <small class="form-text">รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">ยืนยันรหัสผ่านใหม่ *</label>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-control" 
                                   minlength="6" required>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" name="change_password" class="btn btn-warning">
                                🔒 เปลี่ยนรหัสผ่าน
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ตรวจสอบรหัสผ่านตรงกัน
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('รหัสผ่านไม่ตรงกัน');
            } else {
                this.setCustomValidity('');
            }
        });
        
        document.getElementById('new_password').addEventListener('input', function() {
            const confirmPassword = document.getElementById('confirm_password');
            if (confirmPassword.value) {
                confirmPassword.dispatchEvent(new Event('input'));
            }
        });
    </script>
</body>
</html>
