<?php
include 'db.php';
require_once 'vendor/autoload.php';

// Get search parameters
$category = isset($_GET['category']) ? mysqli_real_escape_string($conn, $_GET['category']) : '';
$searchTerm = isset($_GET['term']) ? mysqli_real_escape_string($conn, $_GET['term']) : '';
$isDownload = isset($_GET['download']) && $_GET['download'] === 'true';

// Validate category to prevent SQL injection
$validCategories = ['type', 'brand', 'department', 'location', 'status', 'hostname', 'vendor'];
if (!empty($category) && !in_array($category, $validCategories)) {
    exit('Invalid category');
}

// Configure mPDF
$defaultConfig = (new Mpdf\Config\ConfigVariables())->getDefaults();
$fontDirs = $defaultConfig['fontDir'];

$defaultFontConfig = (new Mpdf\Config\FontVariables())->getDefaults(); 
$fontData = $defaultFontConfig['fontdata'];  

// Create mPDF object with custom configuration
$mpdf = new \Mpdf\Mpdf([
    'fontDir' => array_merge($fontDirs, [
        __DIR__ . '/fonts',
    ]),
    'fontdata' => $fontData + [
        'sarabun' => [ 
            'R' => 'THSarabunNew.ttf', 
            'I' => 'THSarabunNew Italic.ttf',
            'B' => 'THSarabunNew Bold.ttf',
        ] 
    ],
    'default_font' => 'sarabun',
    'margin_left' => 15,
    'margin_right' => 15,
    'margin_top' => 16,
    'margin_bottom' => 16,
    'margin_header' => 9,
    'margin_footer' => 9,
    'orientation' => 'L', // Landscape orientation for better table display
]);

// Set document properties
$mpdf->SetTitle('Asset Management Report');
$mpdf->SetAuthor('Asset Management System');
$mpdf->SetCreator('Asset Management System');

// Set page numbering
$mpdf->SetFooter('Page {PAGENO} of {nb}');

// Build the query based on search parameters
if (!empty($category) && !empty($searchTerm)) {
    // For exact match when using dropdown selection
    if (isset($_GET['exact']) && $_GET['exact'] === 'true') {
        $sql = "SELECT * FROM `tb_data` WHERE $category = '$searchTerm'";
        $title = "รายงานข้อมูล $category: $searchTerm";
    } else {
        // For partial match when typing in search box
        $sql = "SELECT * FROM `tb_data` WHERE $category LIKE '%$searchTerm%'";
        $title = "รายงานข้อมูล $category: $searchTerm";
    }
} else {
    $sql = "SELECT * FROM `tb_data`";
    $title = "รายงานข้อมูล";
}

// Get current date for report
$reportDate = date('d/m/Y');

// Create header with logo and title
$header = '
<table width="100%" style="border: none;">
    <tr>
        <td width="20%" style="text-align: left; border: none;">
        </td>
        <td width="60%" style="text-align: center; border: none;">
            <h1 style="font-size: 24pt;">'.$title.'</h1>
            <p>'.$reportDate.'</p>
        </td>
        <td width="20%" style="text-align: right; border: none;"></td>
    </tr>
</table>
<hr>
';

// Start building the HTML content
$content = '
<style>
body {
    font-family: "sarabun";
    font-size: 12pt;
}
table {
    border-collapse: collapse;
    width: 100%;
    font-size: 14px;
}
th, td {
    text-align: left;
    padding: 6px;
    border: 1px solid #ddd;
}
tr:nth-child(even) {
    background-color: #f2f2f2;
}
th {
    background-color: #4CAF50;
    color: white;
    font-weight: bold;
}
.page-break {
    page-break-after: always;
}
</style>
';

// Add header
$content .= $header;

// Start table
$content .= '
<table autosize="1">
<thead>
<tr>
    <th width="5%">No.</th>
    <th>Type</th>
    <th>Brand</th>
    <th>Model</th>
    <th>Tag</th>
    <th>Department</th>
    <th>Location</th>
    <th>Vendor</th>
    <th>Status</th>
    <th>Hostname</th>
    <th>OS</th>
    <th>SN</th>
    <th>Asset ID</th>
    <th>Warranty Exp</th>
    <th>Description</th>
</tr>
</thead>
<tbody>
';

$result = mysqli_query($conn, $sql);
$rowCount = 0;
$pageBreakAfter = 15; // Number of rows per page
$sequenceNumber = 1; // Initialize sequence counter

while ($row = mysqli_fetch_array($result)) {
    // Format warranty date if it exists
    $warranty_exp = !empty($row['warranty_exp']) ? date('d/m/Y', strtotime($row['warranty_exp'])) : '';
    
    $content .= '
    <tr>
        <td align="center">'.$sequenceNumber.'</td>
        <td>'.$row['type'].'</td>
        <td>'.$row['brand'].'</td>
        <td>'.$row['model'].'</td>
        <td>'.$row['tag'].'</td>
        <td>'.$row['department'].'</td>
        <td>'.$row['location'].'</td>
        <td>'.$row['vendor'].'</td>
        <td>'.$row['status'].'</td>
        <td>'.$row['hostname'].'</td>
        <td>'.$row['os'].'</td>
        <td>'.$row['sn'].'</td>
        <td>'.$row['asset_id'].'</td>
        <td>'.$warranty_exp.'</td>
        <td>'.$row['description'].'</td>
    </tr>
    ';
    
    $rowCount++;
    $sequenceNumber++; // Increment sequence counter
    
    // Add page break after certain number of rows, but not after the last row
    if ($rowCount % $pageBreakAfter === 0 && $rowCount < mysqli_num_rows($result)) {
        $content .= '
        </tbody>
        </table>
        <div class="page-break"></div>
        '.$header.'
        <table autosize="1">
        <thead>
        <tr>
            <th width="5%">No.</th>
            <th>Type</th>
            <th>Brand</th>
            <th>Model</th>
            <th>Tag</th>
            <th>Department</th>
            <th>Location</th>
            <th>Vendor</th>
            <th>Status</th>
            <th>Hostname</th>
            <th>OS</th>
            <th>SN</th>
            <th>Asset ID</th>
            <th>Warranty Exp</th>
            <th>Description</th>
        </tr>
        </thead>
        <tbody>
        ';
    }
}

// Close table
$content .= '
</tbody>
</table>
';

// Add summary section
$content .= '
<div style="margin-top: 20px;">
    <p><strong>จำนวนรายการ:</strong> '.mysqli_num_rows($result).' รายการ</p>
    <p><strong>เงื่อนไขการค้นหา:</strong> '.(!empty($category) && !empty($searchTerm) ? "$category: $searchTerm" : "ทั้งหมด").'</p>
    <p><strong>วันที่ออกรายงาน:</strong> '.$reportDate.'</p>
</div>
';

// Write HTML to PDF
$mpdf->WriteHTML($content);

// Output PDF
$filename = "asset_report_" . ($category ? $category . "_" : "") . ($searchTerm ? preg_replace('/[^a-zA-Z0-9]/', '_', $searchTerm) . "_" : "") . date('d-m-Y');

// Determine output mode - 'I' for inline/browser display, 'D' for download
$outputMode = $isDownload ? 'D' : 'I';
$mpdf->Output($filename.'.pdf', $outputMode);

mysqli_close($conn);
?>
