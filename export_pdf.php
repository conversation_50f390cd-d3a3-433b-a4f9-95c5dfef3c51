<?php
// สร้างการเชื่อมต่อฐานข้อมูลด้วย mysqli
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'asset_management';

$conn = new mysqli($host, $username, $password, $database);
$conn->set_charset("utf8mb4");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

require_once 'vendor/autoload.php';

// ฟังก์ชันสำหรับตั้งค่าฟอนต์ไทย
function setupThaiFont() {
    $fontPath = __DIR__ . "/fonts/THSarabunNew.ttf";

    if (file_exists($fontPath)) {
        try {
            $fontName = TCPDF_FONTS::addTTFfont($fontPath, "TrueTypeUnicode", "", 96);
            return $fontName ? $fontName : "helvetica";
        } catch (Exception $e) {
            return "helvetica"; // fallback
        }
    }

    return "helvetica"; // fallback
}

// รับค่าการค้นหาและกรองจากหน้า index.php
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';
$filter_serial = $_GET['filter_serial'] ?? '';
$isDownload = isset($_GET['download']) && $_GET['download'] === 'true';

// สร้าง TCPDF object
$pdf = new TCPDF('L', PDF_UNIT, 'A4', true, 'UTF-8', false);

// ตั้งค่าข้อมูลเอกสาร
$pdf->SetCreator('Asset Management System');
$pdf->SetAuthor('Asset Management System');
$pdf->SetTitle('Asset Management Report');
$pdf->SetSubject('Asset Report');

// ตั้งค่าหน้า
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
$pdf->SetMargins(15, 15, 15);
$pdf->SetHeaderMargin(5);
$pdf->SetFooterMargin(10);
$pdf->SetAutoPageBreak(TRUE, 15);

// ตั้งค่าฟอนต์ไทย
$thaiFont = setupThaiFont();
$pdf->SetFont($thaiFont, '', 14);

// เพิ่มหน้าแรก
$pdf->AddPage();

// สร้าง SQL query ตามเงื่อนไขการกรอง
$sql = "SELECT * FROM assets WHERE 1=1";
$params = [];
$conditions = [];

// เพิ่มเงื่อนไขการกรอง
if (!empty($search)) {
    $conditions[] = "(type LIKE ? OR brand LIKE ? OR model LIKE ? OR tag LIKE ? OR hostname LIKE ? OR department LIKE ? OR asset_id LIKE ? OR serial_number LIKE ?)";
    $searchTerm = '%' . $search . '%';
    for ($i = 0; $i < 8; $i++) {
        $params[] = $searchTerm;
    }
}

if (!empty($filter_type)) {
    $conditions[] = "type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_brand)) {
    $conditions[] = "brand = ?";
    $params[] = $filter_brand;
}

if (!empty($filter_department)) {
    $conditions[] = "department = ?";
    $params[] = $filter_department;
}

if (!empty($filter_status)) {
    $conditions[] = "status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_os)) {
    $conditions[] = "operating_system = ?";
    $params[] = $filter_os;
}

if (!empty($filter_serial)) {
    $conditions[] = "serial_number LIKE ?";
    $params[] = '%' . $filter_serial . '%';
}

// รวมเงื่อนไข
if (!empty($conditions)) {
    $sql .= " AND " . implode(" AND ", $conditions);
}

$sql .= " ORDER BY created_date DESC";

// สร้างชื่อรายงาน
$title = "รายงานข้อมูล Asset Management";
$filterInfo = [];
if (!empty($search)) $filterInfo[] = "ค้นหา: $search";
if (!empty($filter_type)) $filterInfo[] = "ประเภท: $filter_type";
if (!empty($filter_brand)) $filterInfo[] = "Brand: $filter_brand";
if (!empty($filter_department)) $filterInfo[] = "แผนก: $filter_department";
if (!empty($filter_status)) $filterInfo[] = "สถานะ: $filter_status";
if (!empty($filter_os)) $filterInfo[] = "OS: $filter_os";
if (!empty($filter_serial)) $filterInfo[] = "Serial: $filter_serial";

if (!empty($filterInfo)) {
    $title .= " (" . implode(", ", $filterInfo) . ")";
}

// Get current date for report
$reportDate = date('d/m/Y H:i:s');

// สร้างหัวข้อรายงาน
$pdf->SetFont($thaiFont, 'B', 18);
$pdf->Cell(0, 15, $title, 0, 1, 'C');
$pdf->SetFont($thaiFont, '', 12);
$pdf->Cell(0, 10, 'วันที่ออกรายงาน: ' . $reportDate, 0, 1, 'C');
$pdf->Ln(5);

$pdf->SetFillColor(76, 175, 80); // สีเขียว
$pdf->SetTextColor(255, 255, 255); // สีขาว

// หัวตาราง (เพิ่ม Hostname และ OS หลัง S/N)
$headers = ['ลำดับ', 'Dept.', 'Type', 'Asset ID', 'Tag', 'Model', 'S/N', 'Hostname', 'OS', 'Status'];

// คำนวณความกว้างคอลัมน์ตามเนื้อหา
function calculateColumnWidths($pdf, $headers, $thaiFont) {
    $pdf->SetFont($thaiFont, 'B', 10);
    $colWidths = [];

    // ความกว้างขั้นต่ำสำหรับแต่ละคอลัมน์
    $minWidths = [15, 25, 20, 25, 18, 25, 25, 25, 18, 20];

    foreach ($headers as $i => $header) {
        $headerWidth = $pdf->GetStringWidth($header) + 6; // เพิ่ม padding
        $colWidths[$i] = max($headerWidth, $minWidths[$i]);
    }

    // ปรับความกว้างให้พอดีกับหน้ากระดาษ A4 แนวนอน (ประมาณ 270mm)
    $totalWidth = array_sum($colWidths);
    $maxTableWidth = 250; // ความกว้างสูงสุดของตาราง

    if ($totalWidth > $maxTableWidth) {
        $ratio = $maxTableWidth / $totalWidth;
        foreach ($colWidths as $i => $width) {
            $colWidths[$i] = $width * $ratio;
        }
    }

    return $colWidths;
}

// คำนวณความกว้างคอลัมน์
$colWidths = calculateColumnWidths($pdf, $headers, $thaiFont);

// คำนวณตำแหน่งเริ่มต้นของตารางให้อยู่กึ่งกลาง
$totalTableWidth = array_sum($colWidths);
$pageWidth = 297; // ความกว้างหน้า A4 แนวนอน (mm)
$leftMargin = ($pageWidth - $totalTableWidth) / 2;

// สร้างหัวตารางหน้าแรก
createTableHeader($pdf, $headers, $colWidths, $thaiFont, $leftMargin);

// ฟังก์ชันสำหรับสร้างหัวตาราง
function createTableHeader($pdf, $headers, $colWidths, $thaiFont, $leftMargin) {
    // ตั้งตำแหน่งเริ่มต้นของตาราง
    $pdf->SetX($leftMargin);

    $pdf->SetFont($thaiFont, 'B', 10);
    $pdf->SetFillColor(76, 175, 80);
    $pdf->SetTextColor(255, 255, 255);

    foreach ($headers as $i => $header) {
        $pdf->Cell($colWidths[$i], 8, $header, 1, 0, 'C', true);
    }
    $pdf->Ln();

    $pdf->SetTextColor(0, 0, 0);
    $pdf->SetFont($thaiFont, '', 9);
}

// เตรียม prepared statement
$stmt = $conn->prepare($sql);

// Bind parameters ถ้ามี
if (!empty($params)) {
    $types = str_repeat('s', count($params)); // ใช้ string type สำหรับทุก parameter
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();

$sequenceNumber = 1; // ตัวนับลำดับ
$fill = false; // สำหรับสลับสีแถว
$rowsPerPage = 0; // นับจำนวนแถวในหน้าปัจจุบัน
$maxRowsPerPage = 23; // จำนวนแถวสูงสุดต่อหน้า (หน้าแรก 23 แถว)
$isFirstPage = true; // ตรวจสอบว่าเป็นหน้าแรกหรือไม่

while ($row = $result->fetch_assoc()) {
    // ตรวจสอบว่าต้องขึ้นหน้าใหม่หรือไม่
    $currentMaxRows = $isFirstPage ? 23 : 25; // หน้าแรก 23 แถว, หน้าอื่น 25 แถว

    if ($rowsPerPage >= $currentMaxRows || $pdf->GetY() > 250) {
        $pdf->AddPage();

        // สร้างหัวข้อรายงานในหน้าใหม่
        $pdf->SetFont($thaiFont, 'B', 16);
        $pdf->Cell(0, 10, $title, 0, 1, 'C');
        $pdf->Ln(5);

        // สร้างหัวตารางใหม่
        createTableHeader($pdf, $headers, $colWidths, $thaiFont, $leftMargin);

        $rowsPerPage = 0; // รีเซ็ตตัวนับแถว
        $isFirstPage = false; // ไม่ใช่หน้าแรกแล้ว
    }

    // ตั้งตำแหน่งเริ่มต้นของแถว
    $pdf->SetX($leftMargin);

    // สลับสีแถว
    $pdf->SetFillColor($fill ? 249 : 255, $fill ? 249 : 255, $fill ? 249 : 255);

    // ข้อมูลแถว (เพิ่ม Hostname และ OS)
    $rowData = [
        $sequenceNumber,                                    // ลำดับ
        $row['department'] ?? '',                          // Dept.
        $row['type'] ?? '',                                // Type
        $row['asset_id'] ?? '',                            // Asset ID
        $row['tag'] ?? '',                                 // Tag
        $row['model'] ?? '',                               // Model
        $row['serial_number'] ?? '',                       // S/N
        $row['hostname'] ?? '',                            // Hostname
        $row['operating_system'] ?? '',                    // OS
        $row['status'] ?? ''                               // Status
    ];

    // ปรับข้อความให้พอดีกับคอลัมน์
    foreach ($rowData as $i => $data) {
        // คำนวณความกว้างที่เหมาะสม
        $maxChars = intval($colWidths[$i] / 3); // ประมาณ 3mm ต่อตัวอักษร
        if (strlen($data) > $maxChars && $maxChars > 3) {
            $data = substr($data, 0, $maxChars - 3) . '...';
        }

        $pdf->Cell($colWidths[$i], 6, $data, 1, 0, ($i == 0 ? 'C' : 'L'), $fill);
    }
    $pdf->Ln();

    $sequenceNumber++;
    $rowsPerPage++;
    $fill = !$fill; // สลับสี
}

// เพิ่มส่วนสรุป
$totalRecords = $result->num_rows;
$pdf->Ln(10);

// สร้างกรอบสรุป
$pdf->SetFont($thaiFont, 'B', 12);
$pdf->SetFillColor(240, 240, 240);
$pdf->Cell(0, 8, 'สรุปรายงาน', 1, 1, 'C', true);

$pdf->SetFont($thaiFont, '', 10);
$pdf->SetFillColor(255, 255, 255);

// แสดงข้อมูลสรุปในรูปแบบตาราง
$summaryData = [
    ['รายการ', 'ข้อมูล'],
    ['จำนวนรายการทั้งหมด', $totalRecords . ' รายการ'],
    ['เงื่อนไขการกรอง', !empty($filterInfo) ? implode(", ", $filterInfo) : "ทั้งหมด"],
    ['วันที่ออกรายงาน', $reportDate],
    ['ผู้ออกรายงาน', 'Asset Management System']
];

foreach ($summaryData as $i => $row) {
    $isHeader = ($i == 0);
    $pdf->SetFont($thaiFont, $isHeader ? 'B' : '', 10);
    $pdf->SetFillColor($isHeader ? 220 : 255, $isHeader ? 220 : 255, $isHeader ? 220 : 255);

    $pdf->Cell(60, 6, $row[0], 1, 0, 'L', true);
    $pdf->Cell(0, 6, $row[1], 1, 1, 'L', true);
}

// สร้างชื่อไฟล์
$filename = "asset_report_" . date('d-m-Y_H-i-s');
if (!empty($filter_type)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_type);
if (!empty($filter_brand)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_brand);
if (!empty($filter_department)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_department);

// กำหนดรูปแบบการแสดงผล - 'I' สำหรับแสดงในเบราว์เซอร์, 'D' สำหรับดาวน์โหลด
$outputMode = $isDownload ? 'D' : 'F';

// ส่งออก PDF
if ($isDownload) {
    $pdf->Output($filename . '.pdf', 'D');
} else {
    $pdf->Output($filename . '.pdf', 'I');
}

// ปิดการเชื่อมต่อฐานข้อมูล
$stmt->close();
$conn->close();
?>
