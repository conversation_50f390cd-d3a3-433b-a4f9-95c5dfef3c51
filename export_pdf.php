<?php
// สร้างการเชื่อมต่อฐานข้อมูลด้วย mysqli
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'asset_management';

$conn = new mysqli($host, $username, $password, $database);
$conn->set_charset("utf8mb4");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

require_once 'vendor/autoload.php';

// รับค่าการค้นหาและกรองจากหน้า index.php
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';
$filter_serial = $_GET['filter_serial'] ?? '';
$isDownload = isset($_GET['download']) && $_GET['download'] === 'true';

// สร้าง TCPDF object
$pdf = new TCPDF('L', PDF_UNIT, 'A4', true, 'UTF-8', false);

// ตั้งค่าข้อมูลเอกสาร
$pdf->SetCreator('Asset Management System');
$pdf->SetAuthor('Asset Management System');
$pdf->SetTitle('Asset Management Report');
$pdf->SetSubject('Asset Report');

// ตั้งค่าหน้า
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
$pdf->SetMargins(15, 15, 15);
$pdf->SetHeaderMargin(5);
$pdf->SetFooterMargin(10);
$pdf->SetAutoPageBreak(TRUE, 15);

// ตั้งค่าฟอนต์เริ่มต้น
$pdf->SetFont('helvetica', '', 14);

// เพิ่มหน้าแรก
$pdf->AddPage();

// สร้าง SQL query ตามเงื่อนไขการกรอง
$sql = "SELECT * FROM assets WHERE 1=1";
$params = [];
$conditions = [];

// เพิ่มเงื่อนไขการกรอง
if (!empty($search)) {
    $conditions[] = "(type LIKE ? OR brand LIKE ? OR model LIKE ? OR tag LIKE ? OR hostname LIKE ? OR department LIKE ? OR asset_id LIKE ? OR serial_number LIKE ?)";
    $searchTerm = '%' . $search . '%';
    for ($i = 0; $i < 8; $i++) {
        $params[] = $searchTerm;
    }
}

if (!empty($filter_type)) {
    $conditions[] = "type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_brand)) {
    $conditions[] = "brand = ?";
    $params[] = $filter_brand;
}

if (!empty($filter_department)) {
    $conditions[] = "department = ?";
    $params[] = $filter_department;
}

if (!empty($filter_status)) {
    $conditions[] = "status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_os)) {
    $conditions[] = "operating_system = ?";
    $params[] = $filter_os;
}

if (!empty($filter_serial)) {
    $conditions[] = "serial_number LIKE ?";
    $params[] = '%' . $filter_serial . '%';
}

// รวมเงื่อนไข
if (!empty($conditions)) {
    $sql .= " AND " . implode(" AND ", $conditions);
}

$sql .= " ORDER BY created_date DESC";

// สร้างชื่อรายงาน
$title = "รายงานข้อมูล Asset Management";
$filterInfo = [];
if (!empty($search)) $filterInfo[] = "ค้นหา: $search";
if (!empty($filter_type)) $filterInfo[] = "ประเภท: $filter_type";
if (!empty($filter_brand)) $filterInfo[] = "Brand: $filter_brand";
if (!empty($filter_department)) $filterInfo[] = "แผนก: $filter_department";
if (!empty($filter_status)) $filterInfo[] = "สถานะ: $filter_status";
if (!empty($filter_os)) $filterInfo[] = "OS: $filter_os";
if (!empty($filter_serial)) $filterInfo[] = "Serial: $filter_serial";

if (!empty($filterInfo)) {
    $title .= " (" . implode(", ", $filterInfo) . ")";
}

// Get current date for report
$reportDate = date('d/m/Y H:i:s');

// สร้างหัวข้อรายงาน
$pdf->SetFont('helvetica', 'B', 18);
$pdf->Cell(0, 15, $title, 0, 1, 'C');
$pdf->SetFont('helvetica', '', 12);
$pdf->Cell(0, 10, 'วันที่ออกรายงาน: ' . $reportDate, 0, 1, 'C');
$pdf->Ln(5);

$pdf->SetFillColor(76, 175, 80); // สีเขียว
$pdf->SetTextColor(255, 255, 255); // สีขาว

// กำหนดความกว้างของคอลัมน์
$colWidths = [15, 25, 25, 25, 20, 25, 20, 25, 20, 30, 20, 25, 30];

// หัวตาราง
$headers = ['ลำดับ', 'ประเภท', 'Brand', 'Model', 'Tag', 'แผนก', 'สถานะ', 'Hostname', 'OS', 'Serial Number', 'Asset ID', 'วันหมดประกัน', 'รายละเอียด'];

$pdf->SetFont('helvetica', 'B', 10);
foreach ($headers as $i => $header) {
    $pdf->Cell($colWidths[$i], 8, $header, 1, 0, 'C', true);
}
$pdf->Ln();

// รีเซ็ตสีข้อความ
$pdf->SetTextColor(0, 0, 0);
$pdf->SetFont('helvetica', '', 9);

// เตรียม prepared statement
$stmt = $conn->prepare($sql);

// Bind parameters ถ้ามี
if (!empty($params)) {
    $types = str_repeat('s', count($params)); // ใช้ string type สำหรับทุก parameter
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();

$sequenceNumber = 1; // ตัวนับลำดับ
$fill = false; // สำหรับสลับสีแถว

while ($row = $result->fetch_assoc()) {
    // ตรวจสอบว่าต้องขึ้นหน้าใหม่หรือไม่
    if ($pdf->GetY() > 250) {
        $pdf->AddPage();

        // สร้างหัวตารางใหม่
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->SetFillColor(76, 175, 80);
        $pdf->SetTextColor(255, 255, 255);

        foreach ($headers as $i => $header) {
            $pdf->Cell($colWidths[$i], 8, $header, 1, 0, 'C', true);
        }
        $pdf->Ln();

        $pdf->SetTextColor(0, 0, 0);
        $pdf->SetFont('helvetica', '', 9);
    }

    // Format warranty date if it exists
    $warranty_exp = !empty($row['warranty_expire']) ? date('d/m/Y', strtotime($row['warranty_expire'])) : '-';

    // สลับสีแถว
    $pdf->SetFillColor($fill ? 249 : 255, $fill ? 249 : 255, $fill ? 249 : 255);

    // ข้อมูลแถว
    $rowData = [
        $sequenceNumber,
        $row['type'] ?? '',
        $row['brand'] ?? '',
        $row['model'] ?? '',
        $row['tag'] ?? '',
        $row['department'] ?? '',
        $row['status'] ?? '',
        $row['hostname'] ?? '',
        $row['operating_system'] ?? '',
        $row['serial_number'] ?? '',
        $row['asset_id'] ?? '',
        $warranty_exp,
        substr($row['description'] ?? '', 0, 30) . (strlen($row['description'] ?? '') > 30 ? '...' : '')
    ];

    foreach ($rowData as $i => $data) {
        $pdf->Cell($colWidths[$i], 6, $data, 1, 0, ($i == 0 ? 'C' : 'L'), $fill);
    }
    $pdf->Ln();

    $sequenceNumber++;
    $fill = !$fill; // สลับสี
}

// เพิ่มส่วนสรุป
$totalRecords = $result->num_rows;
$pdf->Ln(10);

$pdf->SetFont('helvetica', 'B', 12);
$pdf->Cell(0, 8, 'สรุปรายงาน', 0, 1, 'L');
$pdf->SetFont('helvetica', '', 11);

$pdf->Cell(0, 6, 'จำนวนรายการทั้งหมด: ' . $totalRecords . ' รายการ', 0, 1, 'L');
$pdf->Cell(0, 6, 'เงื่อนไขการกรอง: ' . (!empty($filterInfo) ? implode(", ", $filterInfo) : "ทั้งหมด"), 0, 1, 'L');
$pdf->Cell(0, 6, 'วันที่ออกรายงาน: ' . $reportDate, 0, 1, 'L');
$pdf->Cell(0, 6, 'ผู้ออกรายงาน: Asset Management System', 0, 1, 'L');

// สร้างชื่อไฟล์
$filename = "asset_report_" . date('d-m-Y_H-i-s');
if (!empty($filter_type)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_type);
if (!empty($filter_brand)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_brand);
if (!empty($filter_department)) $filename .= "_" . preg_replace('/[^a-zA-Z0-9]/', '_', $filter_department);

// กำหนดรูปแบบการแสดงผล - 'I' สำหรับแสดงในเบราว์เซอร์, 'D' สำหรับดาวน์โหลด
$outputMode = $isDownload ? 'D' : 'F';

// ส่งออก PDF
if ($isDownload) {
    $pdf->Output($filename . '.pdf', 'D');
} else {
    $pdf->Output($filename . '.pdf', 'I');
}

// ปิดการเชื่อมต่อฐานข้อมูล
$stmt->close();
$conn->close();
?>
