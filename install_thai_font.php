<?php
// สคริปต์สำหรับติดตั้งฟอนต์ TH Sarabun New ใน TCPDF
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 ติดตั้งฟอนต์ TH Sarabun New สำหรับ TCPDF</h2>";

require_once 'vendor/autoload.php';

// ตรวจสอบว่ามี TCPDF หรือไม่
if (!class_exists('TCPDF')) {
    die("<p style='color: red;'>❌ ไม่พบ TCPDF กรุณาติดตั้งก่อน: composer require tecnickcom/tcpdf</p>");
}

echo "<p style='color: green;'>✅ พบ TCPDF แล้ว</p>";

// สร้างโฟลเดอร์ fonts ถ้ายังไม่มี
$fontsDir = __DIR__ . '/fonts';
if (!is_dir($fontsDir)) {
    mkdir($fontsDir, 0755, true);
    echo "<p style='color: blue;'>📁 สร้างโฟลเดอร์ fonts</p>";
}

// URL ของฟอนต์ TH Sarabun New
$fontUrls = [
    'THSarabunNew.ttf' => 'https://github.com/tlwg/fonts-tlwg/raw/master/tlwg/Sarabun.ttf',
    'THSarabunNew-Bold.ttf' => 'https://github.com/tlwg/fonts-tlwg/raw/master/tlwg/Sarabun-Bold.ttf',
    'THSarabunNew-Italic.ttf' => 'https://github.com/tlwg/fonts-tlwg/raw/master/tlwg/Sarabun-Italic.ttf',
    'THSarabunNew-BoldItalic.ttf' => 'https://github.com/tlwg/fonts-tlwg/raw/master/tlwg/Sarabun-BoldItalic.ttf'
];

// ดาวน์โหลดฟอนต์
foreach ($fontUrls as $filename => $url) {
    $fontPath = $fontsDir . '/' . $filename;
    
    if (!file_exists($fontPath)) {
        echo "<p>📥 กำลังดาวน์โหลด $filename...</p>";
        
        $fontData = file_get_contents($url);
        if ($fontData !== false) {
            file_put_contents($fontPath, $fontData);
            echo "<p style='color: green;'>✅ ดาวน์โหลด $filename สำเร็จ</p>";
        } else {
            echo "<p style='color: red;'>❌ ไม่สามารถดาวน์โหลด $filename ได้</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ $filename มีอยู่แล้ว</p>";
    }
}

// สร้างไฟล์แปลงฟอนต์
$convertScript = '<?php
// สคริปต์แปลงฟอนต์ TH Sarabun New สำหรับ TCPDF
require_once "vendor/autoload.php";

// กำหนดเส้นทางฟอนต์
$fontDir = __DIR__ . "/fonts/";
$tcpdfFontDir = __DIR__ . "/vendor/tecnickcom/tcpdf/fonts/";

// ฟอนต์ที่ต้องการแปลง
$fonts = [
    "thsarabunnew" => [
        "R" => $fontDir . "THSarabunNew.ttf",
        "B" => $fontDir . "THSarabunNew-Bold.ttf", 
        "I" => $fontDir . "THSarabunNew-Italic.ttf",
        "BI" => $fontDir . "THSarabunNew-BoldItalic.ttf"
    ]
];

foreach ($fonts as $fontName => $styles) {
    foreach ($styles as $style => $fontFile) {
        if (file_exists($fontFile)) {
            echo "แปลงฟอนต์: $fontFile\\n";
            
            // ใช้ TCPDF_FONTS::addTTFfont() เพื่อแปลงฟอนต์
            $convertedFont = TCPDF_FONTS::addTTFfont($fontFile, "TrueTypeUnicode", "", 96);
            
            if ($convertedFont) {
                echo "✅ แปลงสำเร็จ: $convertedFont\\n";
            } else {
                echo "❌ แปลงไม่สำเร็จ: $fontFile\\n";
            }
        } else {
            echo "❌ ไม่พบไฟล์: $fontFile\\n";
        }
    }
}

echo "\\n🎉 การแปลงฟอนต์เสร็จสิ้น\\n";
?>';

file_put_contents('convert_fonts.php', $convertScript);
echo "<p style='color: green;'>✅ สร้างไฟล์ convert_fonts.php</p>";

echo "<h3>ขั้นตอนต่อไป:</h3>";
echo "<ol>";
echo "<li>รันคำสั่ง: <code>php convert_fonts.php</code></li>";
echo "<li>ตรวจสอบว่าฟอนต์ถูกแปลงแล้ว</li>";
echo "<li>ทดสอบการใช้งานฟอนต์ในรายงาน PDF</li>";
echo "</ol>";

echo "<p><a href='convert_fonts.php' target='_blank'>🔗 คลิกเพื่อรันการแปลงฟอนต์</a></p>";
echo "<p><a href='test_thai_font.php' target='_blank'>🧪 ทดสอบฟอนต์ไทย</a></p>";
?>
