<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

$message = '';
$messageType = '';

// รับข้อความแจ้งเตือน
$message = $_GET['message'] ?? '';
$messageType = $_GET['type'] ?? 'success';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เครื่องมือ - Asset Management System</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .tool-category {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .tool-category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .tool-category-body {
            padding: 20px;
        }
        
        .tool-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }
        
        .tool-item:last-child {
            border-bottom: none;
        }
        
        .tool-item:hover {
            background: #f8f9fa;
            margin: 0 -20px;
            padding-left: 20px;
            padding-right: 20px;
        }
        
        .tool-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            width: 30px;
            text-align: center;
        }
        
        .tool-info {
            flex: 1;
        }
        
        .tool-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 3px;
        }
        
        .tool-description {
            font-size: 0.85rem;
            color: #718096;
        }
        
        .tool-action {
            margin-left: 10px;
        }
        
        .btn-tool {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            display: inline-block;
        }
        
        .btn-tool:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
            color: white;
        }
        
        .btn-tool.danger {
            background: #e53e3e;
        }
        
        .btn-tool.danger:hover {
            background: #c53030;
        }
        
        .btn-tool.warning {
            background: #dd6b20;
        }
        
        .btn-tool.warning:hover {
            background: #c05621;
        }
        
        .btn-tool.success {
            background: #38a169;
        }
        
        .btn-tool.success:hover {
            background: #2f855a;
        }
        
        .system-info {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .system-info h4 {
            margin: 0 0 10px 0;
            color: #2d3748;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #4a5568;
        }
        
        .info-value {
            color: #2d3748;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ระบบจัดการ Asset</h1>
            <p class="subtitle">Asset Management System - เครื่องมือสำหรับผู้ดูแลระบบ</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="index.php">รายการ Assets</a></li>
                <li><a href="users.php">จัดการผู้ใช้</a></li>
                <li><a href="tools.php" class="active">เครื่องมือ</a></li>
                <li><a href="#" onclick="openProfileModal()">โปรไฟล์</a></li>
                <li><a href="logout.php">ออกจากระบบ</a></li>
            </ul>
        </nav>

        <!-- User Info Bar -->
        <div class="user-info-bar">
            <div class="container">
                <span class="welcome-text">ยินดีต้อนรับ, <strong><?= getCurrentUserFullName() ?></strong></span>
                <span class="role-badge badge badge-success"><?= getCurrentUserRole() ?></span>
            </div>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- System Information -->
        <div class="system-info">
            <h4>🖥️ ข้อมูลระบบ</h4>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">PHP Version:</span>
                    <span class="info-value"><?= phpversion() ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Server:</span>
                    <span class="info-value"><?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Database:</span>
                    <span class="info-value">MySQL</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Timezone:</span>
                    <span class="info-value"><?= date_default_timezone_get() ?></span>
                </div>
            </div>
        </div>

        <!-- Tools Grid -->
        <div class="tools-grid">
            <!-- Database Tools -->
            <div class="tool-category">
                <div class="tool-category-header">
                    🗄️ เครื่องมือฐานข้อมูล
                </div>
                <div class="tool-category-body">
                    <div class="tool-item">
                        <div class="tool-icon">🔍</div>
                        <div class="tool-info">
                            <div class="tool-name">ตรวจสอบโครงสร้างฐานข้อมูล</div>
                            <div class="tool-description">ตรวจสอบและแสดงโครงสร้างตารางในฐานข้อมูล</div>
                        </div>
                        <div class="tool-action">
                            <a href="check_database_structure.php" class="btn-tool">เปิด</a>
                        </div>
                    </div>
                    
                    <div class="tool-item">
                        <div class="tool-icon">🔧</div>
                        <div class="tool-info">
                            <div class="tool-name">แก้ไขโครงสร้างตาราง</div>
                            <div class="tool-description">แก้ไขและปรับปรุงโครงสร้างตาราง Assets</div>
                        </div>
                        <div class="tool-action">
                            <a href="check_and_fix_columns.php" class="btn-tool warning">แก้ไข</a>
                        </div>
                    </div>
                    
                    <div class="tool-item">
                        <div class="tool-icon">🔐</div>
                        <div class="tool-info">
                            <div class="tool-name">แก้ไขระบบ Authentication</div>
                            <div class="tool-description">ตรวจสอบและแก้ไขระบบการเข้าสู่ระบบ</div>
                        </div>
                        <div class="tool-action">
                            <a href="fix_auth_database.php" class="btn-tool warning">แก้ไข</a>
                        </div>
                    </div>

                    <div class="tool-item">
                        <div class="tool-icon">📊</div>
                        <div class="tool-info">
                            <div class="tool-name">ข้อมูลระบบโดยละเอียด</div>
                            <div class="tool-description">ดูข้อมูลระบบ PHP, เซิร์ฟเวอร์ และฐานข้อมูลแบบละเอียด</div>
                        </div>
                        <div class="tool-action">
                            <a href="tools/system_info.php" class="btn-tool">ดูข้อมูล</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Management Tools -->
            <div class="tool-category">
                <div class="tool-category-header">
                    📊 เครื่องมือจัดการข้อมูล
                </div>
                <div class="tool-category-body">
                    <div class="tool-item">
                        <div class="tool-icon">📥</div>
                        <div class="tool-info">
                            <div class="tool-name">Import ข้อมูล CSV</div>
                            <div class="tool-description">นำเข้าข้อมูล Asset จากไฟล์ CSV</div>
                        </div>
                        <div class="tool-action">
                            <a href="import_sample.php" class="btn-tool success">Import</a>
                        </div>
                    </div>
                    
                    <div class="tool-item">
                        <div class="tool-icon">📄</div>
                        <div class="tool-info">
                            <div class="tool-name">ดาวน์โหลด Template CSV</div>
                            <div class="tool-description">ดาวน์โหลดไฟล์ Template สำหรับ Import ข้อมูล</div>
                        </div>
                        <div class="tool-action">
                            <a href="download_template.php" class="btn-tool">ดาวน์โหลด</a>
                        </div>
                    </div>

                    <div class="tool-item">
                        <div class="tool-icon">💾</div>
                        <div class="tool-info">
                            <div class="tool-name">สำรองข้อมูลฐานข้อมูล</div>
                            <div class="tool-description">สร้างไฟล์สำรองข้อมูลฐานข้อมูลทั้งหมด</div>
                        </div>
                        <div class="tool-action">
                            <a href="tools/database_backup.php" class="btn-tool warning">สำรองข้อมูล</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Maintenance Tools -->
            <div class="tool-category">
                <div class="tool-category-header">
                    ⚙️ เครื่องมือบำรุงรักษาระบบ
                </div>
                <div class="tool-category-body">
                    <div class="tool-item">
                        <div class="tool-icon">🧪</div>
                        <div class="tool-info">
                            <div class="tool-name">ทดสอบการเพิ่ม Asset</div>
                            <div class="tool-description">ทดสอบฟังก์ชันการเพิ่ม Asset ใหม่</div>
                        </div>
                        <div class="tool-action">
                            <a href="verify_add_asset_working.php" class="btn-tool">ทดสอบ</a>
                        </div>
                    </div>

                    <div class="tool-item">
                        <div class="tool-icon">📋</div>
                        <div class="tool-info">
                            <div class="tool-name">ตรวจสอบโครงสร้างปัจจุบัน</div>
                            <div class="tool-description">ตรวจสอบสถานะและโครงสร้างระบบปัจจุบัน</div>
                        </div>
                        <div class="tool-action">
                            <a href="check_current_structure.php" class="btn-tool">ตรวจสอบ</a>
                        </div>
                    </div>

                    <div class="tool-item">
                        <div class="tool-icon">🔄</div>
                        <div class="tool-info">
                            <div class="tool-name">อัพเดทโครงสร้างตาราง</div>
                            <div class="tool-description">อัพเดทและปรับปรุงโครงสร้างตารางให้เป็นปัจจุบัน</div>
                        </div>
                        <div class="tool-action">
                            <a href="update_table_structure.php" class="btn-tool warning">อัพเดท</a>
                        </div>
                    </div>

                    <div class="tool-item">
                        <div class="tool-icon">📋</div>
                        <div class="tool-info">
                            <div class="tool-name">ดู Log ระบบ</div>
                            <div class="tool-description">ดูประวัติการเปลี่ยนแปลงและกิจกรรมในระบบ</div>
                        </div>
                        <div class="tool-action">
                            <a href="tools/log_viewer.php" class="btn-tool">ดู Log</a>
                        </div>
                    </div>

                    <div class="tool-item">
                        <div class="tool-icon">🧹</div>
                        <div class="tool-info">
                            <div class="tool-name">ทำความสะอาดระบบ</div>
                            <div class="tool-description">ลบไฟล์ทดสอบและไฟล์ชั่วคราวที่ไม่จำเป็น</div>
                        </div>
                        <div class="tool-action">
                            <a href="tools/cleanup.php" class="btn-tool danger">ทำความสะอาด</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Development Tools -->
            <div class="tool-category">
                <div class="tool-category-header">
                    🛠️ เครื่องมือสำหรับนักพัฒนา
                </div>
                <div class="tool-category-body">
                    <div class="tool-item">
                        <div class="tool-icon">🐛</div>
                        <div class="tool-info">
                            <div class="tool-name">Debug Asset Modal</div>
                            <div class="tool-description">ทดสอบและ Debug Modal สำหรับ Asset</div>
                        </div>
                        <div class="tool-action">
                            <a href="debug_asset_modal.php" class="btn-tool">Debug</a>
                        </div>
                    </div>

                    <div class="tool-item">
                        <div class="tool-icon">🔍</div>
                        <div class="tool-info">
                            <div class="tool-name">ตรวจสอบข้อผิดพลาด</div>
                            <div class="tool-description">แสดงข้อผิดพลาดและ Debug Information</div>
                        </div>
                        <div class="tool-action">
                            <a href="debug_error.php" class="btn-tool">ตรวจสอบ</a>
                        </div>
                    </div>

                    <div class="tool-item">
                        <div class="tool-icon">📊</div>
                        <div class="tool-info">
                            <div class="tool-name">ทดสอบระบบ Import</div>
                            <div class="tool-description">ทดสอบการทำงานของระบบ Import ข้อมูล</div>
                        </div>
                        <div class="tool-action">
                            <a href="test_import_system.php" class="btn-tool">ทดสอบ</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="tool-category">
                <div class="tool-category-header">
                    ⚡ การดำเนินการด่วน
                </div>
                <div class="tool-category-body">
                    <div class="tool-item">
                        <div class="tool-icon">🏠</div>
                        <div class="tool-info">
                            <div class="tool-name">กลับหน้าหลัก</div>
                            <div class="tool-description">กลับไปยังหน้ารายการ Assets</div>
                        </div>
                        <div class="tool-action">
                            <a href="index.php" class="btn-tool success">กลับหน้าหลัก</a>
                        </div>
                    </div>

                    <div class="tool-item">
                        <div class="tool-icon">👥</div>
                        <div class="tool-info">
                            <div class="tool-name">จัดการผู้ใช้</div>
                            <div class="tool-description">ไปยังหน้าจัดการผู้ใช้ระบบ</div>
                        </div>
                        <div class="tool-action">
                            <a href="users.php" class="btn-tool">จัดการผู้ใช้</a>
                        </div>
                    </div>

                    <div class="tool-item">
                        <div class="tool-icon">➕</div>
                        <div class="tool-info">
                            <div class="tool-name">เพิ่ม Asset ใหม่</div>
                            <div class="tool-description">เพิ่ม Asset ใหม่เข้าสู่ระบบ</div>
                        </div>
                        <div class="tool-action">
                            <a href="add_asset.php" class="btn-tool success">เพิ่ม Asset</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Modal -->
    <div id="profileModal" class="modal">
        <div class="modal-content profile-modal" style="max-width: 650px;">
            <div class="modal-header">
                <h2>
                    <span style="font-size: 1.8rem;">👤</span>
                    โปรไฟล์ผู้ใช้
                </h2>
                <span class="close" onclick="closeProfileModal()">&times;</span>
            </div>

            <div class="modal-body">
                <!-- Profile Header -->
                <div class="profile-header">
                    <div class="profile-avatar">
                        <span id="profile_username_display">Loading...</span>
                    </div>
                    <div class="profile-role">
                        <span class="badge badge-success" id="profile_role_text">Loading...</span>
                    </div>
                </div>

                <!-- Profile Information -->
                <div class="profile-info">
                    <table class="profile-table">
                        <tr>
                            <td><strong>Username:</strong></td>
                            <td id="profile_username">-</td>
                        </tr>
                        <tr>
                            <td><strong>ชื่อ-นามสกุล:</strong></td>
                            <td id="profile_full_name">-</td>
                        </tr>
                        <tr>
                            <td><strong>Email:</strong></td>
                            <td id="profile_email">-</td>
                        </tr>
                        <tr>
                            <td><strong>บทบาท:</strong></td>
                            <td id="profile_role">-</td>
                        </tr>
                        <tr>
                            <td><strong>สถานะ:</strong></td>
                            <td id="profile_status">-</td>
                        </tr>
                        <tr>
                            <td><strong>เข้าสู่ระบบล่าสุด:</strong></td>
                            <td id="profile_last_login">-</td>
                        </tr>
                        <tr>
                            <td><strong>วันที่สร้าง:</strong></td>
                            <td id="profile_created_date">-</td>
                        </tr>
                    </table>
                </div>

                <!-- Edit Form (Hidden by default) -->
                <div id="editProfileForm" class="edit-profile-form" style="display: none;">
                    <form>
                        <div class="form-group">
                            <label for="edit_full_name">ชื่อ-นามสกุล:</label>
                            <input type="text" id="edit_full_name" name="full_name" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_email">Email:</label>
                            <input type="email" id="edit_email" name="email" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="current_password">รหัสผ่านปัจจุบัน (ถ้าต้องการเปลี่ยน):</label>
                            <input type="password" id="current_password" name="current_password" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="new_password">รหัสผ่านใหม่:</label>
                            <input type="password" id="new_password" name="new_password" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="confirm_password">ยืนยันรหัสผ่านใหม่:</label>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-control">
                        </div>
                    </form>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" id="editProfileBtn" class="btn btn-primary" onclick="toggleEditMode()">
                    ✏️ แก้ไขโปรไฟล์
                </button>
                <button type="button" id="saveProfileBtn" class="btn btn-success" onclick="saveProfile()" style="display: none;">
                    💾 บันทึก
                </button>
                <button type="button" id="cancelEditBtn" class="btn btn-secondary" onclick="toggleEditMode()" style="display: none;">
                    ❌ ยกเลิก
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeProfileModal()">ปิด</button>
            </div>
        </div>
    </div>

    <script>
        // Profile Modal functions
        function openProfileModal() {
            document.getElementById('profileModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
            loadProfileData();
        }

        function closeProfileModal() {
            document.getElementById('profileModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function loadProfileData() {
            fetch('get_profile_data.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // อัพเดทข้อมูลในส่วน header
                        document.getElementById('profile_username_display').textContent = data.user.username;
                        document.getElementById('profile_role_text').textContent = data.user.role || 'User';

                        // อัพเดทข้อมูลในตาราง
                        document.getElementById('profile_username').textContent = data.user.username;
                        document.getElementById('profile_full_name').textContent = data.user.full_name || '-';
                        document.getElementById('profile_email').textContent = data.user.email || '-';
                        document.getElementById('profile_role').textContent = data.user.role || '-';
                        document.getElementById('profile_status').textContent = data.user.status || '-';
                        document.getElementById('profile_last_login').textContent = data.user.last_login || '-';
                        document.getElementById('profile_created_date').textContent = data.user.created_date || '-';

                        // อัพเดทข้อมูลในฟอร์มแก้ไข
                        document.getElementById('edit_full_name').value = data.user.full_name || '';
                        document.getElementById('edit_email').value = data.user.email || '';
                    } else {
                        alert('ไม่สามารถโหลดข้อมูลโปรไฟล์ได้: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error loading profile:', error);
                    alert('เกิดข้อผิดพลาดในการโหลดข้อมูลโปรไฟล์');
                });
        }

        function toggleEditMode() {
            const profileInfo = document.querySelector('.profile-info');
            const editForm = document.getElementById('editProfileForm');
            const editBtn = document.getElementById('editProfileBtn');
            const saveBtn = document.getElementById('saveProfileBtn');
            const cancelBtn = document.getElementById('cancelEditBtn');

            if (editForm.style.display === 'none') {
                // เข้าสู่โหมดแก้ไข
                profileInfo.style.display = 'none';
                editForm.style.display = 'block';
                editBtn.style.display = 'none';
                saveBtn.style.display = 'inline-block';
                cancelBtn.style.display = 'inline-block';
            } else {
                // ออกจากโหมดแก้ไข
                profileInfo.style.display = 'block';
                editForm.style.display = 'none';
                editBtn.style.display = 'inline-block';
                saveBtn.style.display = 'none';
                cancelBtn.style.display = 'none';

                // รีเซ็ตฟอร์ม
                document.getElementById('current_password').value = '';
                document.getElementById('new_password').value = '';
                document.getElementById('confirm_password').value = '';
            }
        }

        function saveProfile() {
            const formData = new FormData(document.getElementById('editProfileForm').querySelector('form'));

            fetch('update_profile.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('อัพเดทโปรไฟล์สำเร็จ');
                    loadProfileData();
                    toggleEditMode();
                } else {
                    alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถอัพเดทได้'));
                }
            })
            .catch(error => {
                console.error('Error updating profile:', error);
                alert('เกิดข้อผิดพลาดในการอัพเดทโปรไฟล์');
            });
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const profileModal = document.getElementById('profileModal');
            if (event.target == profileModal) {
                closeProfileModal();
            }
        }
    </script>
</body>
</html>
