<?php
// สคริปต์แปลงฟอนต์ TH Sarabun New สำหรับ TCPDF
require_once "vendor/autoload.php";

// กำหนดเส้นทางฟอนต์
$fontDir = __DIR__ . "/fonts/";
$tcpdfFontDir = __DIR__ . "/vendor/tecnickcom/tcpdf/fonts/";

// ฟอนต์ที่ต้องการแปลง
$fonts = [
    "thsarabunnew" => [
        "R" => $fontDir . "THSarabunNew.ttf",
        "B" => $fontDir . "THSarabunNew-Bold.ttf", 
        "I" => $fontDir . "THSarabunNew-Italic.ttf",
        "BI" => $fontDir . "THSarabunNew-BoldItalic.ttf"
    ]
];

foreach ($fonts as $fontName => $styles) {
    foreach ($styles as $style => $fontFile) {
        if (file_exists($fontFile)) {
            echo "แปลงฟอนต์: $fontFile\n";
            
            // ใช้ TCPDF_FONTS::addTTFfont() เพื่อแปลงฟอนต์
            $convertedFont = TCPDF_FONTS::addTTFfont($fontFile, "TrueTypeUnicode", "", 96);
            
            if ($convertedFont) {
                echo "✅ แปลงสำเร็จ: $convertedFont\n";
            } else {
                echo "❌ แปลงไม่สำเร็จ: $fontFile\n";
            }
        } else {
            echo "❌ ไม่พบไฟล์: $fontFile\n";
        }
    }
}

echo "\n🎉 การแปลงฟอนต์เสร็จสิ้น\n";
?>