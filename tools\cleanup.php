<?php
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

$message = '';
$messageType = '';

// รายการไฟล์ทดสอบที่สามารถลบได้
$testFiles = [
    'test_*.php',
    'check_*.php',
    'fix_*.php',
    'verify_*.php',
    'debug_*.php',
    'simple_*.php',
    'quick_*.php',
    'final_*.php',
    'full_*.php'
];

// ค้นหาไฟล์ทดสอบที่มีอยู่
$foundFiles = [];
foreach (glob('../test_*.php') as $file) {
    $foundFiles[] = basename($file);
}
foreach (glob('../check_*.php') as $file) {
    $foundFiles[] = basename($file);
}
foreach (glob('../fix_*.php') as $file) {
    $foundFiles[] = basename($file);
}
foreach (glob('../verify_*.php') as $file) {
    $foundFiles[] = basename($file);
}
foreach (glob('../debug_*.php') as $file) {
    $foundFiles[] = basename($file);
}
foreach (glob('../simple_*.php') as $file) {
    $foundFiles[] = basename($file);
}
foreach (glob('../quick_*.php') as $file) {
    $foundFiles[] = basename($file);
}
foreach (glob('../final_*.php') as $file) {
    $foundFiles[] = basename($file);
}
foreach (glob('../full_*.php') as $file) {
    $foundFiles[] = basename($file);
}

// ลบไฟล์ที่ซ้ำ
$foundFiles = array_unique($foundFiles);
sort($foundFiles);

// ประมวลผลการลบไฟล์
if ($_POST['action'] ?? '' === 'cleanup') {
    $filesToDelete = $_POST['files'] ?? [];
    $deletedCount = 0;
    $errors = [];
    
    foreach ($filesToDelete as $file) {
        $filePath = '../' . $file;
        if (file_exists($filePath) && in_array($file, $foundFiles)) {
            if (unlink($filePath)) {
                $deletedCount++;
            } else {
                $errors[] = $file;
            }
        }
    }
    
    if ($deletedCount > 0) {
        $message = "ลบไฟล์สำเร็จ $deletedCount ไฟล์";
        $messageType = 'success';
        
        // อัพเดทรายการไฟล์
        $foundFiles = array_diff($foundFiles, $filesToDelete);
    }
    
    if (!empty($errors)) {
        $message .= " (ไม่สามารถลบ: " . implode(', ', $errors) . ")";
        $messageType = 'warning';
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทำความสะอาดระบบ - Asset Management System</title>
    <link rel="stylesheet" href="../assets/style.css">
    <style>
        .cleanup-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
            overflow: hidden;
        }
        
        .cleanup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
        }
        
        .cleanup-body {
            padding: 20px;
        }
        
        .file-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .file-item:last-child {
            border-bottom: none;
        }
        
        .file-item:hover {
            background: #e9ecef;
        }
        
        .file-checkbox {
            margin-right: 10px;
        }
        
        .file-name {
            flex: 1;
            font-family: monospace;
            color: #2d3748;
        }
        
        .file-size {
            color: #718096;
            font-size: 0.85rem;
        }
        
        .warning-box {
            background: #fed7d7;
            border: 1px solid #feb2b2;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .warning-box h4 {
            color: #c53030;
            margin: 0 0 10px 0;
        }
        
        .warning-box p {
            color: #742a2a;
            margin: 0;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn-danger {
            background: #e53e3e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-danger:hover {
            background: #c53030;
        }
        
        .select-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .btn-select {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.85rem;
        }
        
        .btn-select:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ทำความสะอาดระบบ</h1>
            <p class="subtitle">System Cleanup - Asset Management System</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="../index.php">รายการ Assets</a></li>
                <li><a href="../users.php">จัดการผู้ใช้</a></li>
                <li><a href="../tools.php">เครื่องมือ</a></li>
                <li><a href="#" onclick="openProfileModal()">โปรไฟล์</a></li>
                <li><a href="../logout.php">ออกจากระบบ</a></li>
            </ul>
        </nav>

        <!-- User Info Bar -->
        <div class="user-info-bar">
            <div class="container">
                <span class="welcome-text">ยินดีต้อนรับ, <strong><?= getCurrentUserFullName() ?></strong></span>
                <span class="role-badge badge badge-success"><?= getCurrentUserRole() ?></span>
            </div>
        </div>

        <!-- Back Button -->
        <div style="margin-bottom: 20px;">
            <a href="../tools.php" class="btn btn-secondary">← กลับไปเครื่องมือ</a>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Warning -->
        <div class="warning-box">
            <h4>⚠️ คำเตือนสำคัญ</h4>
            <p>การลบไฟล์จะไม่สามารถกู้คืนได้ กรุณาตรวจสอบให้แน่ใจว่าไฟล์ที่เลือกไม่จำเป็นต่อการทำงานของระบบ</p>
        </div>

        <!-- Cleanup Section -->
        <div class="cleanup-section">
            <div class="cleanup-header">
                🧹 ลบไฟล์ทดสอบและไฟล์ชั่วคราว
            </div>
            <div class="cleanup-body">
                <?php if (empty($foundFiles)): ?>
                    <div class="empty-state">
                        <div class="icon">✨</div>
                        <h3>ระบบสะอาดแล้ว</h3>
                        <p>ไม่พบไฟล์ทดสอบหรือไฟล์ชั่วคราวที่ต้องลบ</p>
                    </div>
                <?php else: ?>
                    <h3>พบไฟล์ทดสอบ <?= count($foundFiles) ?> ไฟล์</h3>
                    <p>เลือกไฟล์ที่ต้องการลบ:</p>
                    
                    <form method="POST" onsubmit="return confirmDelete()">
                        <input type="hidden" name="action" value="cleanup">
                        
                        <div class="select-buttons">
                            <button type="button" class="btn-select" onclick="selectAll()">เลือกทั้งหมด</button>
                            <button type="button" class="btn-select" onclick="selectNone()">ยกเลิกทั้งหมด</button>
                        </div>
                        
                        <div class="file-list">
                            <?php foreach ($foundFiles as $file): ?>
                                <?php
                                $filePath = '../' . $file;
                                $fileSize = file_exists($filePath) ? filesize($filePath) : 0;
                                ?>
                                <div class="file-item">
                                    <input type="checkbox" name="files[]" value="<?= htmlspecialchars($file) ?>" 
                                           class="file-checkbox" id="file_<?= md5($file) ?>">
                                    <label for="file_<?= md5($file) ?>" class="file-name">
                                        📄 <?= htmlspecialchars($file) ?>
                                    </label>
                                    <span class="file-size">
                                        <?= number_format($fileSize / 1024, 2) ?> KB
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="action-buttons">
                            <button type="submit" class="btn-danger">
                                🗑️ ลบไฟล์ที่เลือก
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function selectAll() {
            const checkboxes = document.querySelectorAll('.file-checkbox');
            checkboxes.forEach(cb => cb.checked = true);
        }
        
        function selectNone() {
            const checkboxes = document.querySelectorAll('.file-checkbox');
            checkboxes.forEach(cb => cb.checked = false);
        }
        
        function confirmDelete() {
            const checked = document.querySelectorAll('.file-checkbox:checked');
            if (checked.length === 0) {
                alert('กรุณาเลือกไฟล์ที่ต้องการลบ');
                return false;
            }
            
            return confirm(`คุณต้องการลบไฟล์ ${checked.length} ไฟล์หรือไม่?\n\nการดำเนินการนี้ไม่สามารถกู้คืนได้`);
        }
    </script>
</body>
</html>
