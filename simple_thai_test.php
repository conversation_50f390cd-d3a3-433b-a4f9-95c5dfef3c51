<?php
// ทดสอบฟอนต์ไทยแบบง่าย
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🧪 ทดสอบฟอนต์ไทยแบบง่าย</h2>";

require_once 'vendor/autoload.php';

try {
    // สร้าง PDF
    $pdf = new TCPDF('P', PDF_UNIT, 'A4', true, 'UTF-8', false);
    $pdf->AddPage();
    
    echo "<p style='color: green;'>✅ สร้าง TCPDF สำเร็จ</p>";
    
    // ทดสอบฟอนต์ต่างๆ
    $fonts = ['helvetica', 'dejavusans', 'freeserif'];
    $testText = 'ทดสอบภาษาไทย Asset Management System';
    
    $y = 30;
    
    foreach ($fonts as $font) {
        try {
            $pdf->SetXY(15, $y);
            $pdf->SetFont($font, 'B', 14);
            $pdf->Cell(0, 10, "ฟอนต์: $font", 0, 1, 'L');
            
            $y += 10;
            $pdf->SetXY(15, $y);
            $pdf->SetFont($font, '', 12);
            $pdf->Cell(0, 8, $testText, 0, 1, 'L');
            
            $y += 15;
            echo "<p style='color: green;'>✅ ฟอนต์ $font ใช้งานได้</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ ฟอนต์ $font ใช้งานไม่ได้: " . $e->getMessage() . "</p>";
        }
    }
    
    // ทดสอบฟอนต์ TH Sarabun
    $fontPath = __DIR__ . "/fonts/THSarabunNew.ttf";
    
    if (file_exists($fontPath)) {
        echo "<p style='color: blue;'>📁 พบไฟล์ฟอนต์ TH Sarabun</p>";
        
        try {
            $thaiFont = TCPDF_FONTS::addTTFfont($fontPath, "TrueTypeUnicode", "", 96);
            
            if ($thaiFont) {
                echo "<p style='color: green;'>✅ แปลงฟอนต์ TH Sarabun สำเร็จ: $thaiFont</p>";
                
                $pdf->SetXY(15, $y);
                $pdf->SetFont($thaiFont, 'B', 14);
                $pdf->Cell(0, 10, "ฟอนต์: TH Sarabun New", 0, 1, 'L');
                
                $y += 10;
                $pdf->SetXY(15, $y);
                $pdf->SetFont($thaiFont, '', 12);
                $pdf->Cell(0, 8, $testText, 0, 1, 'L');
                
                echo "<p style='color: green;'>🎉 ฟอนต์ TH Sarabun ใช้งานได้แล้ว!</p>";
                
            } else {
                echo "<p style='color: red;'>❌ ไม่สามารถแปลงฟอนต์ TH Sarabun ได้</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ เกิดข้อผิดพลาดกับฟอนต์ TH Sarabun: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ ไม่พบไฟล์ฟอนต์ TH Sarabun ที่ $fontPath</p>";
        echo "<p>กรุณารัน <a href='setup_thai_font.php'>setup_thai_font.php</a> ก่อน</p>";
    }
    
    // ส่งออก PDF
    $pdf->Output('simple_thai_test.pdf', 'I');
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
