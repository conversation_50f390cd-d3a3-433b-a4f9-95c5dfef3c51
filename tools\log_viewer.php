<?php
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // ดึงข้อมูล logs
    $limit = $_GET['limit'] ?? 50;
    $action_type = $_GET['action_type'] ?? '';
    
    $sql = "SELECT al.*, a.asset_id, a.type, a.brand, a.model 
            FROM asset_logs al 
            LEFT JOIN assets a ON al.asset_id = a.id";
    
    $params = [];
    if ($action_type) {
        $sql .= " WHERE al.action_type = ?";
        $params[] = $action_type;
    }
    
    $sql .= " ORDER BY al.changed_date DESC LIMIT ?";
    $params[] = (int)$limit;
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // ดึงประเภท action ที่มีอยู่
    $actionTypes = $pdo->query("SELECT DISTINCT action_type FROM asset_logs ORDER BY action_type")->fetchAll(PDO::FETCH_COLUMN);
    
    // สถิติ logs
    $totalLogs = $pdo->query("SELECT COUNT(*) FROM asset_logs")->fetchColumn();
    $todayLogs = $pdo->query("SELECT COUNT(*) FROM asset_logs WHERE DATE(changed_date) = CURDATE()")->fetchColumn();
    $weekLogs = $pdo->query("SELECT COUNT(*) FROM asset_logs WHERE changed_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)")->fetchColumn();
    
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ดู Log ระบบ - Asset Management System</title>
    <link rel="stylesheet" href="../assets/style.css">
    <style>
        .log-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #718096;
            font-size: 0.9rem;
            margin-top: 5px;
        }
        
        .filter-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .log-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .log-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.3s ease;
        }
        
        .log-item:hover {
            background: #f8f9fa;
        }
        
        .log-item:last-child {
            border-bottom: none;
        }
        
        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .log-action {
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        
        .action-create { background: #c6f6d5; color: #22543d; }
        .action-update { background: #bee3f8; color: #2a4365; }
        .action-delete { background: #fed7d7; color: #742a2a; }
        .action-import { background: #e6fffa; color: #234e52; }
        
        .log-time {
            color: #718096;
            font-size: 0.85rem;
        }
        
        .log-details {
            color: #4a5568;
            margin-bottom: 5px;
        }
        
        .log-asset {
            font-size: 0.85rem;
            color: #718096;
        }
        
        .log-user {
            font-weight: 600;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ดู Log ระบบ</h1>
            <p class="subtitle">System Logs - Asset Management System</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="../index.php">รายการ Assets</a></li>
                <li><a href="../users.php">จัดการผู้ใช้</a></li>
                <li><a href="../tools.php">เครื่องมือ</a></li>
                <li><a href="#" onclick="openProfileModal()">โปรไฟล์</a></li>
                <li><a href="../logout.php">ออกจากระบบ</a></li>
            </ul>
        </nav>

        <!-- User Info Bar -->
        <div class="user-info-bar">
            <div class="container">
                <span class="welcome-text">ยินดีต้อนรับ, <strong><?= getCurrentUserFullName() ?></strong></span>
                <span class="role-badge badge badge-success"><?= getCurrentUserRole() ?></span>
            </div>
        </div>

        <!-- Back Button -->
        <div style="margin-bottom: 20px;">
            <a href="../tools.php" class="btn btn-secondary">← กลับไปเครื่องมือ</a>
        </div>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                เกิดข้อผิดพลาด: <?= htmlspecialchars($error) ?>
            </div>
        <?php else: ?>

        <!-- Log Statistics -->
        <div class="log-stats">
            <div class="stat-card">
                <div class="stat-number"><?= number_format($totalLogs) ?></div>
                <div class="stat-label">Log ทั้งหมด</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= number_format($todayLogs) ?></div>
                <div class="stat-label">Log วันนี้</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= number_format($weekLogs) ?></div>
                <div class="stat-label">Log 7 วันที่ผ่านมา</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= count($logs) ?></div>
                <div class="stat-label">แสดงผล</div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <h3>🔍 กรองข้อมูล Log</h3>
            <form method="GET">
                <div class="filter-row">
                    <div class="form-group">
                        <label for="action_type">ประเภทการดำเนินการ:</label>
                        <select id="action_type" name="action_type" class="form-control">
                            <option value="">ทั้งหมด</option>
                            <?php foreach ($actionTypes as $type): ?>
                                <option value="<?= htmlspecialchars($type) ?>" 
                                        <?= $action_type === $type ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($type) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="limit">จำนวนที่แสดง:</label>
                        <select id="limit" name="limit" class="form-control">
                            <option value="25" <?= $limit == 25 ? 'selected' : '' ?>>25 รายการ</option>
                            <option value="50" <?= $limit == 50 ? 'selected' : '' ?>>50 รายการ</option>
                            <option value="100" <?= $limit == 100 ? 'selected' : '' ?>>100 รายการ</option>
                            <option value="200" <?= $limit == 200 ? 'selected' : '' ?>>200 รายการ</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">🔍 กรอง</button>
                        <a href="log_viewer.php" class="btn btn-secondary">🔄 รีเซ็ต</a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Logs Display -->
        <div class="log-table">
            <?php if (empty($logs)): ?>
                <div class="empty-state" style="padding: 40px;">
                    <div class="icon">📋</div>
                    <h3>ไม่พบ Log</h3>
                    <p>ไม่มี Log ที่ตรงกับเงื่อนไขการค้นหา</p>
                </div>
            <?php else: ?>
                <?php foreach ($logs as $log): ?>
                    <div class="log-item">
                        <div class="log-header">
                            <span class="log-action action-<?= strtolower($log['action_type']) ?>">
                                <?= htmlspecialchars($log['action_type']) ?>
                            </span>
                            <span class="log-time">
                                <?= formatDateTime($log['changed_date']) ?>
                            </span>
                        </div>
                        
                        <div class="log-details">
                            <?= htmlspecialchars($log['description']) ?>
                        </div>
                        
                        <div class="log-asset">
                            <?php if ($log['asset_id']): ?>
                                Asset: <strong><?= htmlspecialchars($log['asset_id'] ?? 'N/A') ?></strong>
                                <?php if ($log['type']): ?>
                                    (<?= htmlspecialchars($log['type']) ?> - <?= htmlspecialchars($log['brand']) ?> <?= htmlspecialchars($log['model']) ?>)
                                <?php endif; ?>
                            <?php else: ?>
                                System Action
                            <?php endif; ?>
                            | โดย: <span class="log-user"><?= htmlspecialchars($log['changed_by']) ?></span>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <?php endif; ?>
    </div>
</body>
</html>
