<?php
require_once 'includes/auth.php';

// ถ้าล็อกอินแล้วให้ไปหน้าหลัก
if ($auth->isLoggedIn()) {
    header('Location: index.php');
    exit;
}

$message = '';
$messageType = '';



// ประมวลผลการลงทะเบียน
if ($_POST) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $fullName = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    
    // ตรวจสอบข้อมูล
    if (empty($username) || empty($password) || empty($confirmPassword) || empty($fullName)) {
        $message = 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน';
        $messageType = 'danger';
    } elseif (strlen($username) < 3) {
        $message = 'Username ต้องมีอย่างน้อย 3 ตัวอักษร';
        $messageType = 'danger';
    } elseif (strlen($password) < 6) {
        $message = 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
        $messageType = 'danger';
    } elseif ($password !== $confirmPassword) {
        $message = 'รหัสผ่านไม่ตรงกัน';
        $messageType = 'danger';
    } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = 'รูปแบบ Email ไม่ถูกต้อง';
        $messageType = 'danger';
    } else {
        // ตรวจสอบว่า username ซ้ำหรือไม่
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
        $stmt->execute([$username]);
        
        if ($stmt->fetchColumn() > 0) {
            $message = 'Username นี้มีผู้ใช้แล้ว กรุณาเลือก Username อื่น';
            $messageType = 'danger';
        } else {
            // สร้างผู้ใช้ใหม่ตามฟิลด์ที่มีอยู่
            $userData = [
                'username' => $username,
                'password' => $password,
                'full_name' => $fullName,
                'email' => $email,
                'role' => 'User', // ผู้ใช้ใหม่จะเป็น User เสมอ
                'status' => 'Active'
            ];

            if ($auth->createUser($userData)) {
                $message = 'ลงทะเบียนสำเร็จ! กรุณาเข้าสู่ระบบ';
                $messageType = 'success';

                // เคลียร์ข้อมูลฟอร์ม
                $_POST = [];
            } else {
                $message = 'เกิดข้อผิดพลาดในการลงทะเบียน กรุณาลองใหม่';
                $messageType = 'danger';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ลงทะเบียน - Asset Management System</title>

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <link rel="stylesheet" href="assets/style.css">
    <style>
        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px 0;
        }
        
        .register-card {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }
        
        .register-header {
            margin-bottom: 30px;
        }
        
        .register-header h1 {
            color: #2d3748;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .register-header p {
            color: #718096;
            font-size: 1rem;
        }
        
        .register-form .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        .register-form .form-control {
            padding: 15px;
            font-size: 1rem;
            border-radius: 8px;
        }
        
        .register-btn {
            width: 100%;
            padding: 15px;
            font-size: 1rem;
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .login-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        
        .login-link a:hover {
            text-decoration: underline;
        }
        
        .password-requirements {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .password-requirements ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .password-requirements li {
            margin-bottom: 5px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        @media (max-width: 768px) {
            .register-card {
                margin: 10px;
                padding: 30px 20px;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-card">
            <div class="register-header">
                <h1>ลงทะเบียน</h1>
                <p>สร้างบัญชีผู้ใช้ใหม่</p>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?>">
                    <?= htmlspecialchars($message) ?>
                </div>
            <?php endif; ?>



            <form method="POST" action="" class="register-form">
                <div class="form-group">
                    <label for="username">Username *</label>
                    <input type="text" id="username" name="username" class="form-control"
                           placeholder="กรอก Username (อย่างน้อย 3 ตัวอักษร)" required
                           value="<?= htmlspecialchars($_POST['username'] ?? '') ?>" minlength="3">
                </div>
                
                <div class="form-group">
                    <label for="full_name">ชื่อ-นามสกุล *</label>
                    <input type="text" id="full_name" name="full_name" class="form-control"
                           placeholder="กรอกชื่อ-นามสกุล" required
                           value="<?= htmlspecialchars($_POST['full_name'] ?? '') ?>">
                </div>

                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" class="form-control"
                           placeholder="กรอก Email (ไม่บังคับ)"
                           value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="password">รหัสผ่าน *</label>
                        <input type="password" id="password" name="password" class="form-control" 
                               placeholder="กรอกรหัสผ่าน" required minlength="6">
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">ยืนยันรหัสผ่าน *</label>
                        <input type="password" id="confirm_password" name="confirm_password" class="form-control" 
                               placeholder="ยืนยันรหัสผ่าน" required minlength="6">
                    </div>
                </div>
                
                <div class="password-requirements">
                    <strong>ข้อกำหนดรหัสผ่าน:</strong>
                    <ul>
                        <li>ความยาวอย่างน้อย 6 ตัวอักษร</li>
                        <li>ควรผสมตัวอักษรและตัวเลข</li>
                        <li>หลีกเลี่ยงข้อมูลส่วนตัว</li>
                    </ul>
                </div>
                
                <button type="submit" class="register-btn">📝 ลงทะเบียน</button>
                
                <div class="login-link">
                    มีบัญชีแล้ว? <a href="login.php">เข้าสู่ระบบ</a>
                </div>
            </form>


        </div>
    </div>
    
    <script>
        // ตรวจสอบว่าฟิลด์มีอยู่ก่อนใช้งาน
        document.addEventListener('DOMContentLoaded', function() {
            // ตรวจสอบรหัสผ่านตรงกัน
            const passwordField = document.getElementById('password');
            const confirmPasswordField = document.getElementById('confirm_password');

            if (passwordField && confirmPasswordField) {
                confirmPasswordField.addEventListener('input', function() {
                    const password = passwordField.value;
                    const confirmPassword = this.value;

                    if (password !== confirmPassword) {
                        this.setCustomValidity('รหัสผ่านไม่ตรงกัน');
                    } else {
                        this.setCustomValidity('');
                    }
                });

                passwordField.addEventListener('input', function() {
                    if (confirmPasswordField.value) {
                        confirmPasswordField.dispatchEvent(new Event('input'));
                    }
                });
            }

            // ตรวจสอบ Username
            const usernameField = document.getElementById('username');
            if (usernameField) {
                usernameField.addEventListener('input', function() {
                    const username = this.value;
                    if (username.length > 0 && username.length < 3) {
                        this.setCustomValidity('Username ต้องมีอย่างน้อย 3 ตัวอักษร');
                    } else {
                        this.setCustomValidity('');
                    }
                });

                // Auto focus on username field
                usernameField.focus();
            }
        });
    </script>
</body>
</html>
