<?php
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

$message = '';
$messageType = '';

// ตรวจสอบการดำเนินการ
if ($_POST['action'] ?? '' === 'backup') {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // สร้างชื่อไฟล์ backup
        $backupFile = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        $backupPath = '../backups/' . $backupFile;
        
        // สร้างโฟลเดอร์ backups ถ้ายังไม่มี
        if (!is_dir('../backups')) {
            mkdir('../backups', 0755, true);
        }
        
        // ดึงรายชื่อตาราง
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        $sql = "-- Asset Management System Database Backup\n";
        $sql .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
        $sql .= "-- Database: asset_management\n\n";
        $sql .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
        
        foreach ($tables as $table) {
            // ดึงโครงสร้างตาราง
            $createTable = $pdo->query("SHOW CREATE TABLE `$table`")->fetch();
            $sql .= "-- Table structure for table `$table`\n";
            $sql .= "DROP TABLE IF EXISTS `$table`;\n";
            $sql .= $createTable['Create Table'] . ";\n\n";
            
            // ดึงข้อมูลในตาราง
            $rows = $pdo->query("SELECT * FROM `$table`")->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($rows)) {
                $sql .= "-- Dumping data for table `$table`\n";
                
                foreach ($rows as $row) {
                    $values = array_map(function($value) use ($pdo) {
                        return $value === null ? 'NULL' : $pdo->quote($value);
                    }, array_values($row));
                    
                    $sql .= "INSERT INTO `$table` VALUES (" . implode(', ', $values) . ");\n";
                }
                $sql .= "\n";
            }
        }
        
        $sql .= "SET FOREIGN_KEY_CHECKS=1;\n";
        
        // บันทึกไฟล์
        if (file_put_contents($backupPath, $sql)) {
            $message = "สำรองข้อมูลสำเร็จ! ไฟล์: $backupFile";
            $messageType = 'success';
        } else {
            $message = "ไม่สามารถบันทึกไฟล์ backup ได้";
            $messageType = 'danger';
        }
        
    } catch (Exception $e) {
        $message = "เกิดข้อผิดพลาด: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// ดึงรายการไฟล์ backup ที่มีอยู่
$backupFiles = [];
if (is_dir('../backups')) {
    $files = scandir('../backups');
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $backupFiles[] = [
                'name' => $file,
                'size' => filesize('../backups/' . $file),
                'date' => filemtime('../backups/' . $file)
            ];
        }
    }
    // เรียงตามวันที่ใหม่สุด
    usort($backupFiles, function($a, $b) {
        return $b['date'] - $a['date'];
    });
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>สำรองข้อมูล - Asset Management System</title>
    <link rel="stylesheet" href="../assets/style.css">
    <style>
        .backup-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
            overflow: hidden;
        }
        
        .backup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
        }
        
        .backup-body {
            padding: 20px;
        }
        
        .backup-form {
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .backup-btn {
            background: #38a169;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .backup-btn:hover {
            background: #2f855a;
            transform: translateY(-2px);
        }
        
        .backup-list {
            margin-top: 20px;
        }
        
        .backup-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 10px;
            background: white;
        }
        
        .backup-info {
            flex: 1;
        }
        
        .backup-name {
            font-weight: 600;
            color: #2d3748;
        }
        
        .backup-details {
            font-size: 0.85rem;
            color: #718096;
            margin-top: 5px;
        }
        
        .backup-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-download {
            background: #3182ce;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.85rem;
            transition: all 0.3s ease;
        }
        
        .btn-download:hover {
            background: #2c5aa0;
            color: white;
        }
        
        .warning-box {
            background: #fed7d7;
            border: 1px solid #feb2b2;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .warning-box h4 {
            color: #c53030;
            margin: 0 0 10px 0;
        }
        
        .warning-box p {
            color: #742a2a;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>สำรองข้อมูล</h1>
            <p class="subtitle">Database Backup - Asset Management System</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="../index.php">รายการ Assets</a></li>
                <li><a href="../users.php">จัดการผู้ใช้</a></li>
                <li><a href="../tools.php">เครื่องมือ</a></li>
                <li><a href="#" onclick="openProfileModal()">โปรไฟล์</a></li>
                <li><a href="../logout.php">ออกจากระบบ</a></li>
            </ul>
        </nav>

        <!-- User Info Bar -->
        <div class="user-info-bar">
            <div class="container">
                <span class="welcome-text">ยินดีต้อนรับ, <strong><?= getCurrentUserFullName() ?></strong></span>
                <span class="role-badge badge badge-success"><?= getCurrentUserRole() ?></span>
            </div>
        </div>

        <!-- Back Button -->
        <div style="margin-bottom: 20px;">
            <a href="../tools.php" class="btn btn-secondary">← กลับไปเครื่องมือ</a>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Warning -->
        <div class="warning-box">
            <h4>⚠️ คำเตือนสำคัญ</h4>
            <p>การสำรองข้อมูลจะสร้างไฟล์ SQL ที่มีข้อมูลทั้งหมดในระบบ รวมถึงรหัสผ่านที่เข้ารหัสแล้ว กรุณาเก็บไฟล์ backup ในที่ปลอดภัยและไม่เปิดเผยต่อบุคคลอื่น</p>
        </div>

        <!-- Create Backup Section -->
        <div class="backup-section">
            <div class="backup-header">
                💾 สร้างไฟล์สำรองข้อมูล
            </div>
            <div class="backup-body">
                <div class="backup-form">
                    <h3>สำรองข้อมูลฐานข้อมูล</h3>
                    <p>คลิกปุ่มด้านล่างเพื่อสร้างไฟล์สำรองข้อมูลทั้งหมดในระบบ</p>
                    
                    <form method="POST" onsubmit="return confirm('คุณต้องการสำรองข้อมูลหรือไม่?')">
                        <input type="hidden" name="action" value="backup">
                        <button type="submit" class="backup-btn">
                            💾 สร้างไฟล์สำรองข้อมูล
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Existing Backups Section -->
        <div class="backup-section">
            <div class="backup-header">
                📁 ไฟล์สำรองข้อมูลที่มีอยู่
            </div>
            <div class="backup-body">
                <?php if (empty($backupFiles)): ?>
                    <div class="empty-state">
                        <div class="icon">📁</div>
                        <h3>ไม่มีไฟล์สำรองข้อมูล</h3>
                        <p>ยังไม่มีไฟล์สำรองข้อมูลในระบบ</p>
                    </div>
                <?php else: ?>
                    <div class="backup-list">
                        <?php foreach ($backupFiles as $backup): ?>
                            <div class="backup-item">
                                <div class="backup-info">
                                    <div class="backup-name">📄 <?= htmlspecialchars($backup['name']) ?></div>
                                    <div class="backup-details">
                                        ขนาด: <?= number_format($backup['size'] / 1024, 2) ?> KB | 
                                        วันที่: <?= date('d/m/Y H:i:s', $backup['date']) ?>
                                    </div>
                                </div>
                                <div class="backup-actions">
                                    <a href="../backups/<?= htmlspecialchars($backup['name']) ?>" 
                                       class="btn-download" download>
                                        📥 ดาวน์โหลด
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
