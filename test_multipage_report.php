<?php
// ทดสอบรายงานหลายหน้า
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'vendor/autoload.php';

// เชื่อมต่อฐานข้อมูล
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'asset_management';

$conn = new mysqli($host, $username, $password, $database);
$conn->set_charset("utf8mb4");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// ฟังก์ชันสำหรับตั้งค่าฟอนต์ไทย
function setupThaiFont() {
    $fontPath = __DIR__ . "/fonts/THSarabunNew.ttf";
    
    if (file_exists($fontPath)) {
        try {
            $fontName = TCPDF_FONTS::addTTFfont($fontPath, "TrueTypeUnicode", "", 96);
            return $fontName ? $fontName : "helvetica";
        } catch (Exception $e) {
            return "helvetica";
        }
    }
    
    return "helvetica";
}

// ฟังก์ชันสำหรับสร้างหัวตาราง
function createTableHeader($pdf, $headers, $colWidths, $thaiFont) {
    $pdf->SetFont($thaiFont, 'B', 10);
    $pdf->SetFillColor(76, 175, 80);
    $pdf->SetTextColor(255, 255, 255);
    
    foreach ($headers as $i => $header) {
        $pdf->Cell($colWidths[$i], 8, $header, 1, 0, 'C', true);
    }
    $pdf->Ln();
    
    $pdf->SetTextColor(0, 0, 0);
    $pdf->SetFont($thaiFont, '', 9);
}

try {
    // สร้าง TCPDF object
    $pdf = new TCPDF('L', PDF_UNIT, 'A4', true, 'UTF-8', false);
    
    // ตั้งค่าเอกสาร
    $pdf->SetCreator('Asset Management System');
    $pdf->SetTitle('ทดสอบรายงานหลายหน้า');
    $pdf->SetMargins(15, 15, 15);
    $pdf->SetAutoPageBreak(TRUE, 15);
    
    // ตั้งค่าฟอนต์ไทย
    $thaiFont = setupThaiFont();
    $pdf->SetFont($thaiFont, '', 14);
    
    // เพิ่มหน้าแรก
    $pdf->AddPage();
    
    // หัวข้อรายงาน
    $title = "ทดสอบรายงานหลายหน้า - Asset Management System";
    $reportDate = date('d/m/Y H:i:s');
    
    $pdf->SetFont($thaiFont, 'B', 18);
    $pdf->Cell(0, 15, $title, 0, 1, 'C');
    $pdf->SetFont($thaiFont, '', 12);
    $pdf->Cell(0, 10, 'วันที่ออกรายงาน: ' . $reportDate, 0, 1, 'C');
    $pdf->Ln(5);
    
    // กำหนดความกว้างของคอลัมน์
    $colWidths = [15, 30, 25, 25, 25, 25, 30, 20];
    
    // หัวตาราง
    $headers = ['ลำดับ', 'Dept.', 'Type', 'Asset ID', 'Tag', 'Model', 'S/N', 'Status'];
    
    // สร้างหัวตาราง
    createTableHeader($pdf, $headers, $colWidths, $thaiFont);
    
    // สร้างข้อมูลทดสอบ (50 รายการ)
    $testData = [];
    $departments = ['IT', 'HR', 'Finance', 'Sales', 'Marketing', 'Admin'];
    $types = ['Desktop', 'Laptop', 'Printer', 'Monitor', 'Server'];
    $statuses = ['ใช้งาน', 'ชำรุด', 'สำรอง'];
    
    for ($i = 1; $i <= 50; $i++) {
        $testData[] = [
            'department' => $departments[array_rand($departments)],
            'type' => $types[array_rand($types)],
            'asset_id' => 'AST' . str_pad($i, 4, '0', STR_PAD_LEFT),
            'tag' => 'TAG' . str_pad($i, 3, '0', STR_PAD_LEFT),
            'model' => 'Model-' . $i,
            'serial_number' => 'SN' . str_pad($i, 6, '0', STR_PAD_LEFT),
            'status' => $statuses[array_rand($statuses)]
        ];
    }
    
    $sequenceNumber = 1;
    $fill = false;
    $rowsPerPage = 0;
    $maxRowsPerPage = 25;
    
    foreach ($testData as $row) {
        // ตรวจสอบว่าต้องขึ้นหน้าใหม่หรือไม่
        if ($rowsPerPage >= $maxRowsPerPage || $pdf->GetY() > 250) {
            $pdf->AddPage();
            
            // สร้างหัวข้อรายงานในหน้าใหม่
            $pdf->SetFont($thaiFont, 'B', 16);
            $pdf->Cell(0, 10, $title, 0, 1, 'C');
            $pdf->Ln(5);
            
            // สร้างหัวตารางใหม่
            createTableHeader($pdf, $headers, $colWidths, $thaiFont);
            
            $rowsPerPage = 0;
        }
        
        // สลับสีแถว
        $pdf->SetFillColor($fill ? 249 : 255, $fill ? 249 : 255, $fill ? 249 : 255);
        
        // ข้อมูลแถว
        $rowData = [
            $sequenceNumber,
            $row['department'],
            $row['type'],
            $row['asset_id'],
            $row['tag'],
            $row['model'],
            $row['serial_number'],
            $row['status']
        ];
        
        foreach ($rowData as $i => $data) {
            $pdf->Cell($colWidths[$i], 6, $data, 1, 0, ($i == 0 ? 'C' : 'L'), $fill);
        }
        $pdf->Ln();
        
        $sequenceNumber++;
        $rowsPerPage++;
        $fill = !$fill;
    }
    
    // เพิ่มส่วนสรุป
    $pdf->Ln(10);
    
    $pdf->SetFont($thaiFont, 'B', 12);
    $pdf->SetFillColor(240, 240, 240);
    $pdf->Cell(0, 8, 'สรุปรายงาน', 1, 1, 'C', true);
    
    $pdf->SetFont($thaiFont, '', 10);
    $pdf->SetFillColor(255, 255, 255);
    
    $summaryData = [
        ['รายการ', 'ข้อมูล'],
        ['จำนวนรายการทั้งหมด', count($testData) . ' รายการ'],
        ['จำนวนหน้า', $pdf->getPage() . ' หน้า'],
        ['วันที่ออกรายงาน', $reportDate],
        ['ผู้ออกรายงาน', 'Asset Management System (ทดสอบ)']
    ];
    
    foreach ($summaryData as $i => $row) {
        $isHeader = ($i == 0);
        $pdf->SetFont($thaiFont, $isHeader ? 'B' : '', 10);
        $pdf->SetFillColor($isHeader ? 220 : 255, $isHeader ? 220 : 255, $isHeader ? 220 : 255);
        
        $pdf->Cell(60, 6, $row[0], 1, 0, 'L', true);
        $pdf->Cell(0, 6, $row[1], 1, 1, 'L', true);
    }
    
    // ส่งออก PDF
    $pdf->Output('test_multipage_report.pdf', 'I');
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

$conn->close();
?>
