<?php
// ทดสอบฟอนต์ไทย TH Sarabun New ใน TCPDF
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'vendor/autoload.php';

try {
    // สร้าง TCPDF object
    $pdf = new TCPDF('P', PDF_UNIT, 'A4', true, 'UTF-8', false);
    
    // ตั้งค่าเอกสาร
    $pdf->SetCreator('Asset Management System');
    $pdf->SetTitle('ทดสอบฟอนต์ TH Sarabun New');
    $pdf->SetMargins(15, 15, 15);
    $pdf->SetAutoPageBreak(TRUE, 15);
    
    // เพิ่มหน้า
    $pdf->AddPage();
    
    echo "<h2>🧪 ทดสอบฟอนต์ไทย</h2>";
    
    // ทดสอบฟอนต์ต่างๆ
    $fonts = [
        'helvetica' => 'Helvetica (ฟอนต์เริ่มต้น)',
        'dejavusans' => 'DejaVu Sans',
        'freeserif' => 'FreeSerif',
        'thsarabunnew' => 'TH Sarabun New (ถ้าติดตั้งแล้ว)'
    ];
    
    $testText = 'ทดสอบฟอนต์ภาษาไทย - Asset Management System';
    $testText2 = 'รายงานข้อมูล Asset ประจำวันที่ ' . date('d/m/Y');
    
    $y = 30;
    
    foreach ($fonts as $fontName => $description) {
        try {
            $pdf->SetXY(15, $y);
            $pdf->SetFont($fontName, 'B', 14);
            $pdf->Cell(0, 10, $description, 0, 1, 'L');
            
            $y += 10;
            $pdf->SetXY(15, $y);
            $pdf->SetFont($fontName, '', 12);
            $pdf->Cell(0, 8, $testText, 0, 1, 'L');
            
            $y += 8;
            $pdf->SetXY(15, $y);
            $pdf->SetFont($fontName, '', 10);
            $pdf->Cell(0, 8, $testText2, 0, 1, 'L');
            
            $y += 15;
            
            echo "<p style='color: green;'>✅ ฟอนต์ $fontName ใช้งานได้</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ ฟอนต์ $fontName ใช้งานไม่ได้: " . $e->getMessage() . "</p>";
            
            // ใช้ฟอนต์เริ่มต้นแทน
            $pdf->SetXY(15, $y);
            $pdf->SetFont('helvetica', 'B', 14);
            $pdf->Cell(0, 10, $description . ' (ไม่พร้อมใช้งาน)', 0, 1, 'L');
            $y += 25;
        }
    }
    
    // เพิ่มตารางทดสอบ
    $pdf->SetXY(15, $y + 10);
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Cell(0, 10, 'ตารางทดสอบ', 0, 1, 'L');
    
    $y += 25;
    $pdf->SetXY(15, $y);
    
    // หัวตาราง
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->SetFillColor(76, 175, 80);
    $pdf->SetTextColor(255, 255, 255);
    
    $headers = ['ลำดับ', 'ประเภท', 'ชื่อ', 'รายละเอียด'];
    $widths = [20, 40, 60, 60];
    
    foreach ($headers as $i => $header) {
        $pdf->Cell($widths[$i], 8, $header, 1, 0, 'C', true);
    }
    $pdf->Ln();
    
    // ข้อมูลตาราง
    $pdf->SetTextColor(0, 0, 0);
    $pdf->SetFont('helvetica', '', 9);
    
    $data = [
        ['1', 'Desktop', 'คอมพิวเตอร์ตั้งโต๊ะ', 'สำหรับงานสำนักงาน'],
        ['2', 'Laptop', 'คอมพิวเตอร์พกพา', 'สำหรับงานเคลื่อนที่'],
        ['3', 'Printer', 'เครื่องพิมพ์', 'พิมพ์เอกสาร']
    ];
    
    foreach ($data as $row) {
        foreach ($row as $i => $cell) {
            $pdf->Cell($widths[$i], 6, $cell, 1, 0, 'L');
        }
        $pdf->Ln();
    }
    
    // ส่งออก PDF
    $pdf->Output('test_thai_font.pdf', 'I');
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
