<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'CGIF' => $vendorDir . '/mpdf/mpdf/classes/gif.php',
    'CGIFCOLORTABLE' => $vendorDir . '/mpdf/mpdf/classes/gif.php',
    'CGIFFILEHEADER' => $vendorDir . '/mpdf/mpdf/classes/gif.php',
    'CGIFIMAGE' => $vendorDir . '/mpdf/mpdf/classes/gif.php',
    'CGIFIMAGEHEADER' => $vendorDir . '/mpdf/mpdf/classes/gif.php',
    'CGIFLZW' => $vendorDir . '/mpdf/mpdf/classes/gif.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Datamatrix' => $vendorDir . '/tecnickcom/tcpdf/include/barcodes/datamatrix.php',
    'FPDF_TPL' => $vendorDir . '/setasign/fpdi/fpdf_tpl.php',
    'FPDI' => $vendorDir . '/setasign/fpdi/fpdi.php',
    'FilterASCII85' => $vendorDir . '/setasign/fpdi/filters/FilterASCII85.php',
    'FilterASCIIHexDecode' => $vendorDir . '/setasign/fpdi/filters/FilterASCIIHexDecode.php',
    'FilterLZW' => $vendorDir . '/setasign/fpdi/filters/FilterLZW.php',
    'INDIC' => $vendorDir . '/mpdf/mpdf/classes/indic.php',
    'MYANMAR' => $vendorDir . '/mpdf/mpdf/classes/myanmar.php',
    'OTLdump' => $vendorDir . '/mpdf/mpdf/classes/otl_dump.php',
    'PDF417' => $vendorDir . '/tecnickcom/tcpdf/include/barcodes/pdf417.php',
    'PDFBarcode' => $vendorDir . '/mpdf/mpdf/classes/barcode.php',
    'QRcode' => $vendorDir . '/tecnickcom/tcpdf/include/barcodes/qrcode.php',
    'SEA' => $vendorDir . '/mpdf/mpdf/classes/sea.php',
    'SVG' => $vendorDir . '/mpdf/mpdf/classes/svg.php',
    'TCPDF' => $vendorDir . '/tecnickcom/tcpdf/tcpdf.php',
    'TCPDF2DBarcode' => $vendorDir . '/tecnickcom/tcpdf/tcpdf_barcodes_2d.php',
    'TCPDFBarcode' => $vendorDir . '/tecnickcom/tcpdf/tcpdf_barcodes_1d.php',
    'TCPDF_COLORS' => $vendorDir . '/tecnickcom/tcpdf/include/tcpdf_colors.php',
    'TCPDF_FILTERS' => $vendorDir . '/tecnickcom/tcpdf/include/tcpdf_filters.php',
    'TCPDF_FONTS' => $vendorDir . '/tecnickcom/tcpdf/include/tcpdf_fonts.php',
    'TCPDF_FONT_DATA' => $vendorDir . '/tecnickcom/tcpdf/include/tcpdf_font_data.php',
    'TCPDF_IMAGES' => $vendorDir . '/tecnickcom/tcpdf/include/tcpdf_images.php',
    'TCPDF_STATIC' => $vendorDir . '/tecnickcom/tcpdf/include/tcpdf_static.php',
    'TTFontFile' => $vendorDir . '/mpdf/mpdf/classes/ttfontsuni.php',
    'TTFontFile_Analysis' => $vendorDir . '/mpdf/mpdf/classes/ttfontsuni_analysis.php',
    'UCDN' => $vendorDir . '/mpdf/mpdf/classes/ucdn.php',
    'bmp' => $vendorDir . '/mpdf/mpdf/classes/bmp.php',
    'cssmgr' => $vendorDir . '/mpdf/mpdf/classes/cssmgr.php',
    'directw' => $vendorDir . '/mpdf/mpdf/classes/directw.php',
    'fpdi_pdf_parser' => $vendorDir . '/setasign/fpdi/fpdi_pdf_parser.php',
    'grad' => $vendorDir . '/mpdf/mpdf/classes/grad.php',
    'mPDF' => $vendorDir . '/mpdf/mpdf/mpdf.php',
    'meter' => $vendorDir . '/mpdf/mpdf/classes/meter.php',
    'mpdfform' => $vendorDir . '/mpdf/mpdf/classes/mpdfform.php',
    'otl' => $vendorDir . '/mpdf/mpdf/classes/otl.php',
    'pdf_context' => $vendorDir . '/setasign/fpdi/pdf_context.php',
    'tocontents' => $vendorDir . '/mpdf/mpdf/classes/tocontents.php',
    'wmf' => $vendorDir . '/mpdf/mpdf/classes/wmf.php',
);
