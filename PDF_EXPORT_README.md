# ระบบส่งออกรายงาน PDF - Asset Management System

## คุณสมบัติ

### ✅ ฟีเจอร์ที่พัฒนาเสร็จแล้ว
- ✅ ส่งออกรายงาน PDF ด้วย TCPDF
- ✅ รองรับการกรองข้อมูลตามเงื่อนไขต่างๆ
- ✅ แสดงผลในเบราว์เซอร์หรือดาวน์โหลดไฟล์
- ✅ รองรับฟอนต์ TH Sarabun New (ติดตั้งอัตโนมัติ)
- ✅ รูปแบบตารางที่สวยงาม
- ✅ การแบ่งหน้าอัตโนมัติ (หน้าแรก 23 แถว, หน้าอื่น 25 แถว, แสดงหัวตารางทุกหน้า)
- ✅ ส่วนสรุปรายงาน
- ✅ ปุ่มส่งออกในหน้าหลัก (PDF และ ดาวน์โหลด)
- ✅ ระบบติดตั้งฟอนต์ไทยอัตโนมัติ

### 🔧 การติดตั้งและการใช้งาน

#### ข้อกำหนดระบบ
- PHP 8.0+
- MySQL/MariaDB
- Composer
- TCPDF Library

#### การติดตั้ง
1. ติดตั้ง TCPDF ผ่าน Composer:
```bash
composer require tecnickcom/tcpdf
```

2. ติดตั้งฟอนต์ TH Sarabun New:
```
http://localhost/asset/setup_thai_font.php
```

3. ตรวจสอบไฟล์ที่เกี่ยวข้อง:
- `export_pdf.php` - ไฟล์หลักสำหรับส่งออก PDF
- `index.php` - หน้าหลักที่มีปุ่มส่งออก PDF
- `setup_thai_font.php` - ติดตั้งฟอนต์ไทย
- `test_thai_font.php` - ทดสอบฟอนต์ไทย
- `simple_thai_test.php` - ทดสอบฟอนต์แบบง่าย
- `test_multipage_report.php` - ทดสอบรายงานหลายหน้า

#### การใช้งาน

##### 1. ส่งออก PDF จากหน้าหลัก
- เข้าไปที่หน้า `index.php`
- ใช้ฟิลเตอร์ต่างๆ เพื่อกรองข้อมูล:
  - ค้นหาทั่วไป
  - กรองตามประเภท (Type)
  - กรองตาม Brand
  - กรองตามแผนก (Department)
  - กรองตามสถานะ (Status)
  - กรองตาม Operating System
  - กรองตาม Serial Number
- คลิกปุ่ม "PDF" เพื่อแสดงผลในเบราว์เซอร์
- คลิกปุ่ม "ดาวน์โหลด" เพื่อดาวน์โหลดไฟล์ PDF

##### 2. เรียกใช้โดยตรง
```
http://localhost/asset/export_pdf.php
```

##### 3. เรียกใช้พร้อมพารามิเตอร์
```
http://localhost/asset/export_pdf.php?filter_type=Desktop&filter_status=ใช้งาน&download=true
```

#### พารามิเตอร์ที่รองรับ
- `search` - ค้นหาทั่วไป
- `filter_type` - กรองตามประเภท
- `filter_brand` - กรองตาม Brand
- `filter_department` - กรองตามแผนก
- `filter_status` - กรองตามสถานะ
- `filter_os` - กรองตาม OS
- `filter_serial` - กรองตาม Serial Number
- `download=true` - ดาวน์โหลดไฟล์แทนการแสดงผล

### 📋 รูปแบบรายงาน

#### ข้อมูลที่แสดงในรายงาน (เรียงตามลำดับในตาราง)
1. **ลำดับ** - เลขลำดับ
2. **Dept.** - แผนกที่ใช้งาน (ย่อ)
3. **Type** - ประเภทของ Asset
4. **Asset ID** - รหัส Asset
5. **Tag** - หมายเลข Tag
6. **Model** - รุ่น
7. **S/N** - Serial Number (หมายเลขเครื่อง)
8. **Hostname** - ชื่อเครื่อง
9. **OS** - ระบบปฏิบัติการ
10. **Status** - สถานะการใช้งาน

#### ส่วนสรุปรายงาน
- จำนวนรายการทั้งหมด
- เงื่อนไขการกรอง
- วันที่ออกรายงาน
- ผู้ออกรายงาน

### 🎨 การปรับแต่ง

#### การเปลี่ยนฟอนต์
ระบบจะใช้ฟอนต์ TH Sarabun New อัตโนมัติ หากไม่พบจะใช้ Helvetica แทน

ฟังก์ชัน `setupThaiFont()` ในไฟล์ `export_pdf.php`:
```php
function setupThaiFont() {
    $fontPath = __DIR__ . "/fonts/THSarabunNew.ttf";

    if (file_exists($fontPath)) {
        try {
            $fontName = TCPDF_FONTS::addTTFfont($fontPath, "TrueTypeUnicode", "", 96);
            return $fontName ? $fontName : "helvetica";
        } catch (Exception $e) {
            return "helvetica"; // fallback
        }
    }

    return "helvetica"; // fallback
}
```

#### การปรับขนาดคอลัมน์
แก้ไขอาร์เรย์ `$colWidths` ในไฟล์ `export_pdf.php`:
```php
// ลำดับปัจจุบัน: ลำดับ, Dept., Type, Asset ID, Tag, Model, S/N, Hostname, OS, Status
$colWidths = [12, 25, 20, 20, 18, 20, 25, 20, 15, 18];
```

#### การเปลี่ยนสีหัวตาราง
แก้ไขค่า RGB ในไฟล์ `export_pdf.php`:
```php
$pdf->SetFillColor(76, 175, 80); // R, G, B
```

### 🐛 การแก้ไขปัญหา

#### ปัญหาที่พบบ่อย
1. **ไม่สามารถสร้าง PDF ได้**
   - ตรวจสอบว่าติดตั้ง TCPDF แล้ว
   - ตรวจสอบ PHP version (ต้อง 8.0+)

2. **ฟอนต์ภาษาไทยไม่แสดง**
   - ใช้ฟอนต์ที่รองรับภาษาไทย เช่น 'helvetica', 'dejavusans'
   - ตรวจสอบ encoding เป็น UTF-8
   - หลีกเลี่ยงฟอนต์ที่ไม่มีใน TCPDF เช่น 'thsarabunnew'

3. **ข้อมูลไม่ครบ**
   - ตรวจสอบการเชื่อมต่อฐานข้อมูล
   - ตรวจสอบชื่อตารางและคอลัมน์

### 📝 การพัฒนาต่อ

#### ฟีเจอร์ที่อาจเพิ่มในอนาคต
- [ ] รองรับฟอนต์ TH Sarabun New (ต้องติดตั้งฟอนต์เพิ่มเติม)
- [ ] เพิ่มกราฟและชาร์ต
- [ ] ส่งออกเป็น Excel
- [ ] กำหนดเทมเพลตรายงานได้
- [ ] ส่งรายงานทาง Email
- [ ] เพิ่มโลโก้องค์กรในรายงาน

### 📞 การสนับสนุน

หากพบปัญหาหรือต้องการความช่วยเหลือ:
1. ตรวจสอบไฟล์ `test_pdf.php` เพื่อทดสอบ TCPDF
2. ตรวจสอบ PHP error logs
3. ตรวจสอบการเชื่อมต่อฐานข้อมูล

---
**หมายเหตุ:** ระบบนี้ใช้ TCPDF แทน mPDF เนื่องจากความเข้ากันได้กับ PHP 8.2+
