<?php
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnected = true;
    
    // ดึงข้อมูลสถิติ
    $assetCount = $pdo->query("SELECT COUNT(*) FROM assets")->fetchColumn();
    $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    
    // ดึงข้อมูลตาราง
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
} catch (Exception $e) {
    $dbConnected = false;
    $dbError = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ข้อมูลระบบ - Asset Management System</title>
    <link rel="stylesheet" href="../assets/style.css">
    <style>
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .info-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .info-card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
        }
        
        .info-card-body {
            padding: 20px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #4a5568;
        }
        
        .info-value {
            color: #2d3748;
        }
        
        .status-good {
            color: #38a169;
            font-weight: 600;
        }
        
        .status-bad {
            color: #e53e3e;
            font-weight: 600;
        }
        
        .status-warning {
            color: #dd6b20;
            font-weight: 600;
        }
        
        .table-list {
            max-height: 200px;
            overflow-y: auto;
            background: #f7fafc;
            border-radius: 5px;
            padding: 10px;
        }
        
        .table-item {
            padding: 5px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .table-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ข้อมูลระบบ</h1>
            <p class="subtitle">System Information - Asset Management System</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="../index.php">รายการ Assets</a></li>
                <li><a href="../users.php">จัดการผู้ใช้</a></li>
                <li><a href="../tools.php">เครื่องมือ</a></li>
                <li><a href="#" onclick="openProfileModal()">โปรไฟล์</a></li>
                <li><a href="../logout.php">ออกจากระบบ</a></li>
            </ul>
        </nav>

        <!-- User Info Bar -->
        <div class="user-info-bar">
            <div class="container">
                <span class="welcome-text">ยินดีต้อนรับ, <strong><?= getCurrentUserFullName() ?></strong></span>
                <span class="role-badge badge badge-success"><?= getCurrentUserRole() ?></span>
            </div>
        </div>

        <!-- Back Button -->
        <div style="margin-bottom: 20px;">
            <a href="../tools.php" class="btn btn-secondary">← กลับไปเครื่องมือ</a>
        </div>

        <!-- System Information Grid -->
        <div class="info-grid">
            <!-- PHP Information -->
            <div class="info-card">
                <div class="info-card-header">
                    🐘 ข้อมูล PHP
                </div>
                <div class="info-card-body">
                    <div class="info-item">
                        <span class="info-label">PHP Version:</span>
                        <span class="info-value"><?= phpversion() ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Memory Limit:</span>
                        <span class="info-value"><?= ini_get('memory_limit') ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Max Execution Time:</span>
                        <span class="info-value"><?= ini_get('max_execution_time') ?> seconds</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Upload Max Filesize:</span>
                        <span class="info-value"><?= ini_get('upload_max_filesize') ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Post Max Size:</span>
                        <span class="info-value"><?= ini_get('post_max_size') ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Timezone:</span>
                        <span class="info-value"><?= date_default_timezone_get() ?></span>
                    </div>
                </div>
            </div>

            <!-- Server Information -->
            <div class="info-card">
                <div class="info-card-header">
                    🖥️ ข้อมูลเซิร์ฟเวอร์
                </div>
                <div class="info-card-body">
                    <div class="info-item">
                        <span class="info-label">Server Software:</span>
                        <span class="info-value"><?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Server Name:</span>
                        <span class="info-value"><?= $_SERVER['SERVER_NAME'] ?? 'Unknown' ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Document Root:</span>
                        <span class="info-value"><?= $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown' ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Server Port:</span>
                        <span class="info-value"><?= $_SERVER['SERVER_PORT'] ?? 'Unknown' ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">HTTPS:</span>
                        <span class="info-value"><?= isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'Yes' : 'No' ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Current Time:</span>
                        <span class="info-value"><?= date('Y-m-d H:i:s') ?></span>
                    </div>
                </div>
            </div>

            <!-- Database Information -->
            <div class="info-card">
                <div class="info-card-header">
                    🗄️ ข้อมูลฐานข้อมูล
                </div>
                <div class="info-card-body">
                    <div class="info-item">
                        <span class="info-label">Connection Status:</span>
                        <span class="info-value <?= $dbConnected ? 'status-good' : 'status-bad' ?>">
                            <?= $dbConnected ? '✅ Connected' : '❌ Disconnected' ?>
                        </span>
                    </div>
                    
                    <?php if ($dbConnected): ?>
                        <div class="info-item">
                            <span class="info-label">Database Name:</span>
                            <span class="info-value">asset_management</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Total Assets:</span>
                            <span class="info-value"><?= number_format($assetCount) ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Total Users:</span>
                            <span class="info-value"><?= number_format($userCount) ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Tables:</span>
                            <span class="info-value"><?= count($tables) ?> tables</span>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <strong>Table List:</strong>
                            <div class="table-list">
                                <?php foreach ($tables as $table): ?>
                                    <div class="table-item">📋 <?= htmlspecialchars($table) ?></div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="info-item">
                            <span class="info-label">Error:</span>
                            <span class="info-value status-bad"><?= htmlspecialchars($dbError) ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- PHP Extensions -->
            <div class="info-card">
                <div class="info-card-header">
                    🔧 PHP Extensions
                </div>
                <div class="info-card-body">
                    <?php
                    $requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'session'];
                    foreach ($requiredExtensions as $ext):
                        $loaded = extension_loaded($ext);
                    ?>
                        <div class="info-item">
                            <span class="info-label"><?= $ext ?>:</span>
                            <span class="info-value <?= $loaded ? 'status-good' : 'status-bad' ?>">
                                <?= $loaded ? '✅ Loaded' : '❌ Not Loaded' ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
