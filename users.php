<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

$message = '';
$messageType = '';

// รับข้อความแจ้งเตือน
$message = $_GET['message'] ?? '';
$messageType = $_GET['type'] ?? 'success';

// ดึงรายการผู้ใช้ทั้งหมด
$users = $auth->getAllUsers();
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการผู้ใช้ - Asset Management System</title>

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><i class="fas fa-users"></i> ระบบจัดการผู้ใช้</h1>
            <p class="subtitle">User Management System - จัดการผู้ใช้งานระบบอย่างมีประสิทธิภาพ</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="index.php"><i class="fas fa-list"></i> รายการ Assets</a></li>
                <?php if (isAdmin()): ?>
                    <li><a href="users.php" class="active"><i class="fas fa-users"></i> จัดการผู้ใช้</a></li>
                <?php endif; ?>
                <li><a href="#" onclick="openToolsModal()"><i class="fas fa-tools"></i> เครื่องมือ</a></li>
                <li><a href="#" onclick="openProfileModal()"><i class="fas fa-user"></i> โปรไฟล์</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> ออกจากระบบ</a></li>
            </ul>
        </nav>

        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <i class="fas fa-check-circle"></i>
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-info">
                    <h3><?= count($users) ?></h3>
                    <p>ผู้ใช้ทั้งหมด</p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon admin">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="stat-info">
                    <h3><?= count(array_filter($users, function($user) { return ($user['role'] ?? 'Admin') === 'Admin'; })) ?></h3>
                    <p>ผู้ดูแลระบบ</p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon user">
                    <i class="fas fa-user"></i>
                </div>
                <div class="stat-info">
                    <h3><?= count(array_filter($users, function($user) { return ($user['role'] ?? 'Admin') === 'User'; })) ?></h3>
                    <p>ผู้ใช้ทั่วไป</p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon active">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-info">
                    <h3><?= count(array_filter($users, function($user) { return ($user['status'] ?? 'Active') === 'Active'; })) ?></h3>
                    <p>ผู้ใช้ที่ใช้งานอยู่</p>
                </div>
            </div>
        </div>

        <!-- Users Management -->
        <div class="card">
            <div class="card-header">
                <div class="header-content">
                    <h2><i class="fas fa-users"></i> รายการผู้ใช้</h2>
                    <div class="header-actions">
                        <a href="register.php" class="btn btn-success">
                            <i class="fas fa-user-plus"></i> เพิ่มผู้ใช้ใหม่
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($users)): ?>
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3>ไม่มีผู้ใช้ในระบบ</h3>
                        <p>ยังไม่มีผู้ใช้ในระบบ เริ่มต้นด้วยการเพิ่มผู้ใช้แรก</p>
                        <a href="register.php" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> เพิ่มผู้ใช้แรก
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> ID</th>
                                    <th><i class="fas fa-user"></i> Username</th>
                                    <th><i class="fas fa-id-card"></i> ชื่อ-นามสกุล</th>
                                    <th><i class="fas fa-envelope"></i> Email</th>
                                    <?php
                                    // ตรวจสอบว่ามี columns เหล่านี้หรือไม่
                                    $hasRole = !empty($users) && isset($users[0]['role']);
                                    $hasStatus = !empty($users) && isset($users[0]['status']);
                                    $hasLastLogin = !empty($users) && isset($users[0]['last_login']);
                                    $hasCreatedDate = !empty($users) && isset($users[0]['created_date']);
                                    ?>
                                    <?php if ($hasRole): ?><th><i class="fas fa-user-tag"></i> Role</th><?php endif; ?>
                                    <?php if ($hasStatus): ?><th><i class="fas fa-circle"></i> Status</th><?php endif; ?>
                                    <?php if ($hasLastLogin): ?><th><i class="fas fa-clock"></i> Last Login</th><?php endif; ?>
                                    <?php if ($hasCreatedDate): ?><th><i class="fas fa-calendar-plus"></i> วันที่สร้าง</th><?php endif; ?>
                                    <th><i class="fas fa-cogs"></i> การจัดการ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td>
                                            <span class="user-id">#<?= $user['id'] ?></span>
                                        </td>
                                        <td>
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <span class="username"><?= htmlspecialchars($user['username']) ?></span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="full-name"><?= htmlspecialchars($user['full_name'] ?? $user['username']) ?></span>
                                        </td>
                                        <td>
                                            <span class="email"><?= htmlspecialchars($user['email'] ?? '-') ?></span>
                                        </td>
                                        <?php if ($hasRole): ?>
                                        <td>
                                            <?php if (isset($user['role'])): ?>
                                                <span class="role-badge <?= $user['role'] === 'Admin' ? 'admin' : 'user' ?>">
                                                    <i class="fas fa-<?= $user['role'] === 'Admin' ? 'user-shield' : 'user' ?>"></i>
                                                    <?= htmlspecialchars($user['role']) ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="role-badge admin">
                                                    <i class="fas fa-user-shield"></i>
                                                    Admin
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <?php endif; ?>
                                        <?php if ($hasStatus): ?>
                                        <td>
                                            <?php if (isset($user['status'])): ?>
                                                <span class="status-badge <?= $user['status'] === 'Active' ? 'active' : 'inactive' ?>">
                                                    <i class="fas fa-circle"></i>
                                                    <?= htmlspecialchars($user['status']) ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="status-badge active">
                                                    <i class="fas fa-circle"></i>
                                                    Active
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <?php endif; ?>
                                        <?php if ($hasLastLogin): ?>
                                        <td>
                                            <span class="datetime"><?= isset($user['last_login']) && $user['last_login'] ? formatDateTime($user['last_login']) : '-' ?></span>
                                        </td>
                                        <?php endif; ?>
                                        <?php if ($hasCreatedDate): ?>
                                        <td>
                                            <span class="datetime"><?= isset($user['created_date']) ? formatDateTime($user['created_date']) : '-' ?></span>
                                        </td>
                                        <?php endif; ?>
                                        <td>
                                            <div class="action-buttons">
                                                <button onclick="openEditUserModal(<?= $user['id'] ?>)" class="btn btn-warning" title="แก้ไข">✏️</button>
                                                <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                    <a href="delete_user.php?id=<?= $user['id'] ?>" class="btn btn-danger" title="ลบ"
                                                       onclick="return confirm('คุณแน่ใจหรือไม่ที่จะลบผู้ใช้นี้?')">🗑️</a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div id="editUserModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🔧 แก้ไขผู้ใช้</h2>
                <span class="close" onclick="closeEditUserModal()">&times;</span>
            </div>

            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="edit_user_id" name="user_id">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_user_username">🏷️ Username *</label>
                            <input type="text" id="edit_user_username" name="username" class="form-control"
                                   required minlength="3" placeholder="กรอก Username">
                        </div>
                        <div class="form-group">
                            <label for="edit_user_full_name">👤 ชื่อ-นามสกุล *</label>
                            <input type="text" id="edit_user_full_name" name="full_name" class="form-control"
                                   required placeholder="กรอกชื่อ-นามสกุล">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_user_email">📧 Email</label>
                            <input type="email" id="edit_user_email" name="email" class="form-control"
                                   placeholder="กรอก Email">
                        </div>
                        <div class="form-group">
                            <label for="edit_user_role">🎭 บทบาท</label>
                            <select id="edit_user_role" name="role" class="form-control">
                                <option value="User">👤 User</option>
                                <option value="Admin">👑 Admin</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_user_status">🟢 สถานะ</label>
                            <select id="edit_user_status" name="status" class="form-control">
                                <option value="Active">🟢 Active</option>
                                <option value="Inactive">🔴 Inactive</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit_user_password">🔑 รหัสผ่านใหม่</label>
                            <input type="password" id="edit_user_password" name="password" class="form-control"
                                   placeholder="กรอกรหัสผ่านใหม่ (ถ้าต้องการเปลี่ยน)" minlength="6">
                            <small class="form-text">ปล่อยว่างไว้หากไม่ต้องการเปลี่ยนรหัสผ่าน</small>
                        </div>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-success" onclick="saveUserChanges()">
                    💾 บันทึกการเปลี่ยนแปลง
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeEditUserModal()">ยกเลิก</button>
            </div>
        </div>
    </div>

    <script>
        // Profile Modal functions
        function openProfileModal() {
            document.getElementById('profileModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
            loadProfileData();
        }

        function closeProfileModal() {
            document.getElementById('profileModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function loadProfileData() {
            fetch('get_profile_data.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // อัพเดทข้อมูลในส่วน header
                        document.getElementById('profile_username_display').textContent = data.user.username;
                        document.getElementById('profile_role_text').textContent = data.user.role || 'User';

                        // อัพเดทข้อมูลในตาราง
                        document.getElementById('profile_username').textContent = data.user.username;
                        document.getElementById('profile_full_name').textContent = data.user.full_name || '-';
                        document.getElementById('profile_email').textContent = data.user.email || '-';

                        // อัพเดท role badge
                        const roleBadge = document.getElementById('profile_role');
                        roleBadge.textContent = data.user.role || '-';
                        roleBadge.className = 'role-badge' + (data.user.role === 'Admin' ? ' admin' : '');

                        // อัพเดท status badge
                        const statusBadge = document.getElementById('profile_status');
                        statusBadge.textContent = data.user.status || '-';
                        statusBadge.className = 'status-badge' + (data.user.status === 'Inactive' ? ' inactive' : '');

                        document.getElementById('profile_last_login').textContent = data.user.last_login || '-';
                        document.getElementById('profile_created_date').textContent = data.user.created_date || '-';

                        // Set form values for editing
                        document.getElementById('edit_username').value = data.user.username;
                        document.getElementById('edit_full_name').value = data.user.full_name || '';
                        document.getElementById('edit_email').value = data.user.email || '';

                        // อัพเดทไอคอน role ใน header
                        const roleDisplay = document.getElementById('profile_role_display');
                        const roleIcon = data.user.role === 'Admin' ? '👑' : '👤';
                        roleDisplay.querySelector('span:first-child').textContent = roleIcon;
                    }
                })
                .catch(error => {
                    console.error('Error loading profile:', error);
                    alert('เกิดข้อผิดพลาดในการโหลดข้อมูลโปรไฟล์');
                });
        }

        function toggleEditMode() {
            const viewMode = document.getElementById('profileViewMode');
            const editMode = document.getElementById('profileEditMode');

            if (viewMode.style.display === 'none') {
                viewMode.style.display = 'block';
                editMode.style.display = 'none';
            } else {
                viewMode.style.display = 'none';
                editMode.style.display = 'block';
            }
        }

        function saveProfile() {
            const formData = new FormData(document.getElementById('editProfileForm'));

            fetch('update_profile.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('อัพเดทโปรไฟล์สำเร็จ');
                    loadProfileData();
                    toggleEditMode();
                } else {
                    alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถอัพเดทได้'));
                }
            })
            .catch(error => {
                console.error('Error updating profile:', error);
                alert('เกิดข้อผิดพลาดในการอัพเดทโปรไฟล์');
            });
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const profileModal = document.getElementById('profileModal');
            if (event.target == profileModal) {
                closeProfileModal();
            }
        }

        // Add loading animation for stats
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Users management page loaded');
        });
    </script>

    <!-- Profile Modal -->
    <div id="profileModal" class="modal">
        <div class="modal-content profile-modal" style="max-width: 650px;">
            <div class="modal-header">
                <h2>
                    <span style="font-size: 1.8rem;">👤</span>
                    โปรไฟล์ผู้ใช้
                </h2>
                <span class="close" onclick="closeProfileModal()">&times;</span>
            </div>

            <!-- View Mode -->
            <div id="profileViewMode" class="modal-body">
                <div class="profile-info">
                    <div class="profile-avatar">
                        <div class="avatar-circle">
                            <span style="font-size: 3.5rem;">👤</span>
                        </div>
                    </div>

                    <div class="profile-user-info">
                        <div class="profile-username" id="profile_username_display">-</div>
                        <div class="profile-role-display" id="profile_role_display">
                            <span>👑</span>
                            <span id="profile_role_text">-</span>
                        </div>
                    </div>

                    <div class="profile-details">
                        <div class="detail-row">
                            <label>🏷️ Username</label>
                            <span id="profile_username">-</span>
                        </div>

                        <div class="detail-row">
                            <label>👤 ชื่อ-นามสกุล</label>
                            <span id="profile_full_name">-</span>
                        </div>

                        <div class="detail-row">
                            <label>📧 Email</label>
                            <span id="profile_email">-</span>
                        </div>

                        <div class="detail-row">
                            <label>🎭 บทบาท</label>
                            <span class="badge-container">
                                <span id="profile_role" class="role-badge">-</span>
                            </span>
                        </div>

                        <div class="detail-row">
                            <label>🟢 สถานะ</label>
                            <span class="badge-container">
                                <span id="profile_status" class="status-badge">-</span>
                            </span>
                        </div>

                        <div class="detail-row">
                            <label>🕐 เข้าสู่ระบบล่าสุด</label>
                            <span id="profile_last_login">-</span>
                        </div>

                        <div class="detail-row">
                            <label>📅 วันที่สร้างบัญชี</label>
                            <span id="profile_created_date">-</span>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="toggleEditMode()">
                        ✏️ แก้ไขโปรไฟล์
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeProfileModal()">ปิด</button>
                </div>
            </div>

            <!-- Edit Mode -->
            <div id="profileEditMode" class="modal-body" style="display: none;">
                <div class="profile-edit-form">
                    <form id="editProfileForm">
                        <div class="form-group">
                            <label for="edit_username">🏷️ Username</label>
                            <input type="text" id="edit_username" name="username" class="form-control" readonly>
                            <small class="form-text">Username ไม่สามารถเปลี่ยนแปลงได้</small>
                        </div>

                        <div class="form-group">
                            <label for="edit_full_name">👤 ชื่อ-นามสกุล</label>
                            <input type="text" id="edit_full_name" name="full_name" class="form-control" required placeholder="กรอกชื่อ-นามสกุลของคุณ">
                        </div>

                        <div class="form-group">
                            <label for="edit_email">📧 Email</label>
                            <input type="email" id="edit_email" name="email" class="form-control" placeholder="กรอกอีเมลของคุณ">
                        </div>

                        <div class="form-group">
                            <label for="edit_current_password">🔐 รหัสผ่านปัจจุบัน</label>
                            <input type="password" id="edit_current_password" name="current_password" class="form-control" placeholder="กรอกรหัสผ่านปัจจุบัน">
                            <small class="form-text">กรอกเฉพาะเมื่อต้องการเปลี่ยนรหัสผ่าน</small>
                        </div>

                        <div class="form-group">
                            <label for="edit_new_password">🔑 รหัสผ่านใหม่</label>
                            <input type="password" id="edit_new_password" name="new_password" class="form-control" placeholder="กรอกรหัสผ่านใหม่">
                        </div>

                        <div class="form-group">
                            <label for="edit_confirm_password">✅ ยืนยันรหัสผ่านใหม่</label>
                            <input type="password" id="edit_confirm_password" name="confirm_password" class="form-control" placeholder="กรอกรหัสผ่านใหม่อีกครั้ง">
                        </div>
                    </form>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-success" onclick="saveProfile()">
                        💾 บันทึก
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="toggleEditMode()">ยกเลิก</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Edit User Modal functions
        function openEditUserModal(userId) {
            console.log('Opening edit modal for user ID:', userId);

            // ดึงข้อมูลผู้ใช้
            fetch(`get_user_data.php?id=${userId}`)
                .then(response => {
                    console.log('Get user response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(text => {
                    console.log('Get user response text:', text);
                    try {
                        const data = JSON.parse(text);
                        if (data.success) {
                            // กำหนดค่าให้ฟอร์ม
                            document.getElementById('edit_user_id').value = data.user.id;
                            document.getElementById('edit_user_username').value = data.user.username || '';
                            document.getElementById('edit_user_full_name').value = data.user.full_name || '';
                            document.getElementById('edit_user_email').value = data.user.email || '';
                            document.getElementById('edit_user_role').value = data.user.role || 'User';
                            document.getElementById('edit_user_status').value = data.user.status || 'Active';
                            document.getElementById('edit_user_password').value = '';

                            // แสดง modal
                            document.getElementById('editUserModal').style.display = 'block';
                            document.body.style.overflow = 'hidden';
                        } else {
                            alert('ไม่สามารถดึงข้อมูลผู้ใช้ได้: ' + (data.message || 'Unknown error'));
                        }
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        alert('เกิดข้อผิดพลาดในการประมวลผลข้อมูล');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการดึงข้อมูล: ' + error.message);
                });
        }

        function closeEditUserModal() {
            document.getElementById('editUserModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function saveUserChanges() {
            const form = document.getElementById('editUserForm');
            const formData = new FormData(form);

            // ตรวจสอบข้อมูล
            const username = formData.get('username').trim();
            const fullName = formData.get('full_name').trim();
            const password = formData.get('password');

            if (username.length < 3) {
                alert('Username ต้องมีอย่างน้อย 3 ตัวอักษร');
                return;
            }

            if (!fullName) {
                alert('กรุณากรอกชื่อ-นามสกุล');
                return;
            }

            if (password && password.length < 6) {
                alert('รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร');
                return;
            }

            // แสดง loading
            const saveButton = document.querySelector('#editUserModal .btn-success');
            const originalText = saveButton.textContent;
            saveButton.textContent = '⏳ กำลังบันทึก...';
            saveButton.disabled = true;

            // ส่งข้อมูล
            fetch('update_user.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text(); // ใช้ text() ก่อนเพื่อดู response
            })
            .then(text => {
                console.log('Response text:', text);
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        alert('อัพเดทข้อมูลผู้ใช้สำเร็จ');
                        closeEditUserModal();
                        location.reload(); // รีโหลดหน้าเพื่อแสดงข้อมูลใหม่
                    } else {
                        alert(data.message || 'เกิดข้อผิดพลาดในการอัพเดทข้อมูล');
                    }
                } catch (e) {
                    console.error('JSON parse error:', e);
                    alert('เกิดข้อผิดพลาดในการประมวลผลข้อมูล');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('เกิดข้อผิดพลาดในการส่งข้อมูล: ' + error.message);
            })
            .finally(() => {
                // คืนค่าปุ่ม
                saveButton.textContent = originalText;
                saveButton.disabled = false;
            });
        }

        // ปิด modal เมื่อคลิกนอก modal
        window.onclick = function(event) {
            const editModal = document.getElementById('editUserModal');
            const profileModal = document.getElementById('profileModal');

            if (event.target === editModal) {
                closeEditUserModal();
            }
            if (event.target === profileModal) {
                closeProfileModal();
            }
        }
    </script>
</body>
</html>
